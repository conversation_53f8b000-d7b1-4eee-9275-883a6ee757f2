from django import forms
from django.utils import timezone
from datetime import date, timedelta
from .models import RegistroIndice

class RegistroIndiceForm(forms.ModelForm):
   # actualizar_usuario = forms.BooleanField(required=False, label="Actualizar usuario que modificó")

    class Meta:
        model = RegistroIndice
        fields = ['numero', 'lugar', 'fecha', 'otorgantes', 'objetivo', 'folio']
        widgets = {
            'fecha': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'numero': forms.NumberInput(attrs={'class': 'form-control'}),
            'lugar': forms.TextInput(attrs={'class': 'form-control'}),
            'otorgantes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'objetivo': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'folio': forms.TextInput(attrs={'class': 'form-control'}),
        }


class ReporteIndicesForm(forms.Form):
    """
    Formulario para generar reportes de índices por rango de fechas
    """
    fecha_inicio = forms.DateField(
        label='Fecha de inicio',
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
            'required': True
        }),
        help_text='Seleccione la fecha de inicio del período a reportar'
    )

    fecha_fin = forms.DateField(
        label='Fecha de fin',
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
            'required': True
        }),
        help_text='Seleccione la fecha de fin del período a reportar'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Establecer valores por defecto
        hoy = timezone.now().date()
        hace_30_dias = hoy - timedelta(days=30)

        if not self.data:
            self.fields['fecha_inicio'].initial = hace_30_dias
            self.fields['fecha_fin'].initial = hoy

    def clean(self):
        cleaned_data = super().clean()
        fecha_inicio = cleaned_data.get('fecha_inicio')
        fecha_fin = cleaned_data.get('fecha_fin')

        if fecha_inicio and fecha_fin:
            # Validar que la fecha de inicio no sea mayor que la fecha de fin
            if fecha_inicio > fecha_fin:
                raise forms.ValidationError(
                    'La fecha de inicio no puede ser mayor que la fecha de fin.'
                )

            # Validar que el rango no sea mayor a 1 año
            if (fecha_fin - fecha_inicio).days > 365:
                raise forms.ValidationError(
                    'El rango de fechas no puede ser mayor a 1 año.'
                )

            # Validar que las fechas no sean futuras
            hoy = timezone.now().date()
            if fecha_inicio > hoy:
                raise forms.ValidationError(
                    'La fecha de inicio no puede ser futura.'
                )
            if fecha_fin > hoy:
                raise forms.ValidationError(
                    'La fecha de fin no puede ser futura.'
                )

        return cleaned_data
