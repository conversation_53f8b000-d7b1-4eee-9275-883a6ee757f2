from django import forms
from .models import RegistroIndice

class RegistroIndiceForm(forms.ModelForm):
   # actualizar_usuario = forms.BooleanField(required=False, label="Actualizar usuario que modificó")

    class Meta:
        model = RegistroIndice
        fields = ['numero', 'lugar', 'fecha', 'otorgantes', 'objetivo', 'folio']
        widgets = {
            'fecha': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'numero': forms.NumberInput(attrs={'class': 'form-control'}),
            'lugar': forms.TextInput(attrs={'class': 'form-control'}),
            'otorgantes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'objetivo': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'folio': forms.TextInput(attrs={'class': 'form-control'}),
        }
