from django.db import models
from django.conf import settings
from django.contrib.auth.models import User

class RegistroIndice(models.Model):
    numero = models.PositiveIntegerField(verbose_name="No.")
    lugar = models.CharField(max_length=100)
    fecha = models.DateField()
    otorgantes = models.TextField()
    objetivo = models.TextField()
    folio = models.CharField(max_length=50)
    creado_por = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='indices_creados')
    modificado_por = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='indices_modificados')

    def __str__(self):
        return f"Índice {self.numero} - Folio {self.folio}"



