from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.pdfgen import canvas
from django.http import HttpResponse
from django.utils import timezone
from datetime import datetime
import io
from .models import RegistroIndice


class ReporteIndicesPDF:
    """
    Clase para generar reportes PDF de índices jurídicos con diseño profesional
    """
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Configurar estilos personalizados para el documento jurídico"""
        # Estilo para el título principal
        self.styles.add(ParagraphStyle(
            name='TituloReporte',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        ))
        
        # Estilo para subtítulos
        self.styles.add(ParagraphStyle(
            name='SubtituloReporte',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=20,
            alignment=TA_LEFT,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        ))
        
        # Estilo para información del encabezado
        self.styles.add(ParagraphStyle(
            name='InfoEncabezado',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            alignment=TA_LEFT,
            fontName='Helvetica'
        ))
        
        # Estilo para contenido de tabla
        self.styles.add(ParagraphStyle(
            name='ContenidoTabla',
            parent=self.styles['Normal'],
            fontSize=9,
            alignment=TA_LEFT,
            fontName='Helvetica'
        ))
        
        # Estilo para pie de página
        self.styles.add(ParagraphStyle(
            name='PiePagina',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey,
            fontName='Helvetica-Oblique'
        ))

    def crear_encabezado(self, fecha_inicio, fecha_fin, total_registros):
        """Crear el encabezado del reporte"""
        elementos = []
        
        # Título principal
        titulo = Paragraph("REPORTE DE ÍNDICES JURÍDICOS", self.styles['TituloReporte'])
        elementos.append(titulo)
        elementos.append(Spacer(1, 20))
        
        # Información del reporte
        fecha_generacion = timezone.now().strftime("%d/%m/%Y %H:%M")
        
        info_reporte = [
            f"<b>Período del reporte:</b> {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}",
            f"<b>Fecha de generación:</b> {fecha_generacion}",
            f"<b>Total de registros:</b> {total_registros}",
            f"<b>Oficina Jurídica</b> - Sistema de Gestión de Índices"
        ]
        
        for info in info_reporte:
            elementos.append(Paragraph(info, self.styles['InfoEncabezado']))
        
        elementos.append(Spacer(1, 20))
        
        # Línea separadora
        elementos.append(Paragraph("_" * 80, self.styles['InfoEncabezado']))
        elementos.append(Spacer(1, 15))
        
        return elementos

    def crear_tabla_indices(self, registros):
        """Crear tabla con los registros de índices"""
        if not registros:
            return [Paragraph("No se encontraron registros en el período seleccionado.", 
                            self.styles['Normal'])]
        
        elementos = []
        
        # Subtítulo
        subtitulo = Paragraph("DETALLE DE REGISTROS", self.styles['SubtituloReporte'])
        elementos.append(subtitulo)
        elementos.append(Spacer(1, 10))
        
        # Encabezados de la tabla
        encabezados = [
            'No.',
            'Fecha',
            'Lugar',
            'Otorgantes',
            'Objetivo',
            'Folio',
            'Creado por'
        ]
        
        # Datos de la tabla
        datos_tabla = [encabezados]
        
        for registro in registros:
            fila = [
                str(registro.numero),
                registro.fecha.strftime('%d/%m/%Y'),
                registro.lugar[:30] + '...' if len(registro.lugar) > 30 else registro.lugar,
                registro.otorgantes[:40] + '...' if len(registro.otorgantes) > 40 else registro.otorgantes,
                registro.objetivo[:50] + '...' if len(registro.objetivo) > 50 else registro.objetivo,
                registro.folio,
                registro.creado_por.username if registro.creado_por else 'N/A'
            ]
            datos_tabla.append(fila)
        
        # Crear tabla
        tabla = Table(datos_tabla, colWidths=[0.8*inch, 1*inch, 1.5*inch, 2*inch, 2.5*inch, 1*inch, 1*inch])
        
        # Estilo de la tabla
        estilo_tabla = TableStyle([
            # Encabezados
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # Contenido
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.white]),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ])
        
        tabla.setStyle(estilo_tabla)
        elementos.append(tabla)
        
        return elementos

    def crear_resumen_estadistico(self, registros, fecha_inicio, fecha_fin):
        """Crear resumen estadístico del período"""
        elementos = []
        
        elementos.append(Spacer(1, 30))
        subtitulo = Paragraph("RESUMEN ESTADÍSTICO", self.styles['SubtituloReporte'])
        elementos.append(subtitulo)
        elementos.append(Spacer(1, 10))
        
        # Calcular estadísticas
        total_registros = len(registros)
        
        # Agrupar por lugar
        lugares = {}
        for registro in registros:
            lugar = registro.lugar
            lugares[lugar] = lugares.get(lugar, 0) + 1
        
        # Agrupar por usuario creador
        usuarios = {}
        for registro in registros:
            usuario = registro.creado_por.username if registro.creado_por else 'N/A'
            usuarios[usuario] = usuarios.get(usuario, 0) + 1
        
        # Crear tabla de resumen
        datos_resumen = [
            ['CONCEPTO', 'CANTIDAD'],
            ['Total de registros', str(total_registros)],
            ['Período analizado', f"{(fecha_fin - fecha_inicio).days + 1} días"],
            ['Promedio diario', f"{total_registros / max((fecha_fin - fecha_inicio).days + 1, 1):.1f}"]
        ]
        
        tabla_resumen = Table(datos_resumen, colWidths=[3*inch, 1.5*inch])
        estilo_resumen = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.darkblue),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ])
        tabla_resumen.setStyle(estilo_resumen)
        elementos.append(tabla_resumen)
        
        return elementos

    def generar_reporte(self, fecha_inicio, fecha_fin):
        """Generar el reporte PDF completo"""
        # Filtrar registros por fecha
        registros = RegistroIndice.objects.filter(
            fecha__range=[fecha_inicio, fecha_fin]
        ).order_by('fecha', 'numero')
        
        # Crear buffer para el PDF
        buffer = io.BytesIO()
        
        # Crear documento
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Elementos del documento
        elementos = []
        
        # Agregar encabezado
        elementos.extend(self.crear_encabezado(fecha_inicio, fecha_fin, len(registros)))
        
        # Agregar tabla de registros
        elementos.extend(self.crear_tabla_indices(registros))
        
        # Agregar resumen estadístico
        elementos.extend(self.crear_resumen_estadistico(registros, fecha_inicio, fecha_fin))
        
        # Pie de página
        elementos.append(Spacer(1, 30))
        pie = Paragraph(
            f"Reporte generado el {timezone.now().strftime('%d/%m/%Y a las %H:%M')} | "
            f"Oficina Jurídica - Sistema de Gestión de Índices",
            self.styles['PiePagina']
        )
        elementos.append(pie)
        
        # Construir PDF
        doc.build(elementos)
        
        # Obtener el PDF del buffer
        pdf = buffer.getvalue()
        buffer.close()
        
        return pdf


def generar_reporte_indices_pdf(fecha_inicio, fecha_fin):
    """
    Función principal para generar reporte de índices en PDF
    
    Args:
        fecha_inicio (date): Fecha de inicio del período
        fecha_fin (date): Fecha de fin del período
    
    Returns:
        HttpResponse: Respuesta HTTP con el PDF generado
    """
    generador = ReporteIndicesPDF()
    pdf_content = generador.generar_reporte(fecha_inicio, fecha_fin)
    
    # Crear respuesta HTTP
    response = HttpResponse(pdf_content, content_type='application/pdf')
    filename = f"reporte_indices_{fecha_inicio.strftime('%Y%m%d')}_{fecha_fin.strftime('%Y%m%d')}.pdf"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    return response
