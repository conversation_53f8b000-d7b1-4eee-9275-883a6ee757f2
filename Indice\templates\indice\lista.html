{% extends "BaseInicio/base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>Listado de Índices</h2>
    <a class="btn btn-primary mb-3" href="{% url 'agregar_indice' %}">Agregar nuevo</a>
    <table class="table table-bordered">
        <thead class="table-warning">
            <tr>
                <th>No.</th>
                <th>Lugar</th>
                <th>Fecha</th>
                <th>Otorgantes</th>
                <th>Objetivo</th>
                <th>Folio</th>
                <th>Creado por</th>
                <th>Modificado por</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for registro in registros %}
            <tr>
                <td>{{ registro.numero }}</td>
                <td>{{ registro.lugar }}</td>
                <td>{{ registro.fecha|date:"d/m/Y" }}</td>
                <td>{{ registro.otorgantes }}</td>
                <td>{{ registro.objetivo }}</td>
                <td>{{ registro.folio }}</td>
                <td>{{ registro.creado_por.username }}</td>
                <td>{{ registro.modificado_por.username|default:"—" }}</td>
                <td>
                    <a class="btn btn-sm btn-warning" href="{% url 'editar_indice' registro.pk %}">Editar</a>
                </td>
            </tr>
            {% empty %}
            <tr><td colspan="9">No hay registros.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
