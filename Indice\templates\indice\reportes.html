{% extends "BaseInicio/base.html" %}

{% block content %}
<div class="container mt-4">
    <!-- Encabezado de la página -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-pdf me-2"></i>
                        Generación de Reportes de Índices
                    </h3>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-0">
                        Genere reportes en PDF de los registros de índices jurídicos por rango de fechas.
                        El reporte incluye información detallada y estadísticas del período seleccionado.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas rápidas -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <h4>{{ total_registros }}</h4>
                    <p class="mb-0">Total de Registros</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                    <h4>PDF</h4>
                    <p class="mb-0">Formato de Reporte</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <h4>Descarga</h4>
                    <p class="mb-0">Automática</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de generación de reportes -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Filtros del Reporte
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Mostrar mensajes de error -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" id="reporteForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.fecha_inicio.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    {{ form.fecha_inicio.label }}
                                </label>
                                {{ form.fecha_inicio }}
                                {% if form.fecha_inicio.help_text %}
                                    <div class="form-text">{{ form.fecha_inicio.help_text }}</div>
                                {% endif %}
                                {% if form.fecha_inicio.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.fecha_inicio.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.fecha_fin.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-calendar-day me-1"></i>
                                    {{ form.fecha_fin.label }}
                                </label>
                                {{ form.fecha_fin }}
                                {% if form.fecha_fin.help_text %}
                                    <div class="form-text">{{ form.fecha_fin.help_text }}</div>
                                {% endif %}
                                {% if form.fecha_fin.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.fecha_fin.errors %}
                                            <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Errores generales del formulario -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Botones de acción -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'lista_indices' %}" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Volver al Listado
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="btnGenerar">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        Generar Reporte PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Información del Reporte
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Contenido del Reporte:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Información detallada de cada registro</li>
                                <li><i class="fas fa-check text-success me-2"></i>Datos de otorgantes y objetivos</li>
                                <li><i class="fas fa-check text-success me-2"></i>Información de folios y lugares</li>
                                <li><i class="fas fa-check text-success me-2"></i>Usuario que creó cada registro</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Estadísticas Incluidas:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-chart-bar text-primary me-2"></i>Total de registros del período</li>
                                <li><i class="fas fa-chart-bar text-primary me-2"></i>Promedio diario de registros</li>
                                <li><i class="fas fa-chart-bar text-primary me-2"></i>Distribución por lugares</li>
                                <li><i class="fas fa-chart-bar text-primary me-2"></i>Registros por usuario</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Nota:</strong> El rango de fechas no puede exceder 1 año y las fechas no pueden ser futuras.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript para mejorar la experiencia del usuario -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('reporteForm');
    const btnGenerar = document.getElementById('btnGenerar');
    
    form.addEventListener('submit', function() {
        // Cambiar el texto del botón mientras se genera el reporte
        btnGenerar.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generando Reporte...';
        btnGenerar.disabled = true;
        
        // Restaurar el botón después de 5 segundos (tiempo estimado de generación)
        setTimeout(function() {
            btnGenerar.innerHTML = '<i class="fas fa-file-pdf me-1"></i>Generar Reporte PDF';
            btnGenerar.disabled = false;
        }, 5000);
    });
    
    // Validación en tiempo real de fechas
    const fechaInicio = document.getElementById('{{ form.fecha_inicio.id_for_label }}');
    const fechaFin = document.getElementById('{{ form.fecha_fin.id_for_label }}');
    
    function validarFechas() {
        if (fechaInicio.value && fechaFin.value) {
            const inicio = new Date(fechaInicio.value);
            const fin = new Date(fechaFin.value);
            
            if (inicio > fin) {
                fechaFin.setCustomValidity('La fecha de fin debe ser mayor o igual a la fecha de inicio');
            } else {
                fechaFin.setCustomValidity('');
            }
        }
    }
    
    fechaInicio.addEventListener('change', validarFechas);
    fechaFin.addEventListener('change', validarFechas);
});
</script>
{% endblock %}
