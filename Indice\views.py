from django.shortcuts import get_object_or_404, render, redirect
from .models import RegistroIndice
from .forms import RegistroIndiceForm
from django.contrib.auth.decorators import login_required


@login_required
def lista_indices(request):
    registros = RegistroIndice.objects.all().order_by('numero')
    return render(request, 'indice/lista.html', {'registros': registros})


@login_required
def agregar_indice(request):
    if request.method == 'POST':
        form = RegistroIndiceForm(request.POST)
        if form.is_valid():
            instancia = form.save(commit=False)
            instancia.creado_por = request.user
            instancia.save()
            return redirect('lista_indices')
    else:
        form = RegistroIndiceForm()
    return render(request, 'indice/agregar.html', {'form': form})



@login_required
def editar_indice(request, pk):
    registro = get_object_or_404(RegistroIndice, pk=pk)
    if request.method == 'POST':
        form = RegistroIndiceForm(request.POST, instance=registro)
        if form.is_valid():
            instancia = form.save(commit=False)
            instancia.modificado_por = request.user  # Aquí asignamos automáticamente
            instancia.save()
            return redirect('lista_indices')
    else:
        form = RegistroIndiceForm(instance=registro)
    return render(request, 'indice/editar.html', {'form': form, 'registro': registro})


