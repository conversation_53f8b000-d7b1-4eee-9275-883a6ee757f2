from django.shortcuts import get_object_or_404, render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .models import RegistroIndice
from .forms import RegistroIndiceForm, ReporteIndicesForm
from .reportes import generar_reporte_indices_pdf


@login_required
def lista_indices(request):
    registros = RegistroIndice.objects.all().order_by('numero')
    return render(request, 'indice/lista.html', {'registros': registros})


@login_required
def agregar_indice(request):
    if request.method == 'POST':
        form = RegistroIndiceForm(request.POST)
        if form.is_valid():
            instancia = form.save(commit=False)
            instancia.creado_por = request.user
            instancia.save()
            return redirect('lista_indices')
    else:
        form = RegistroIndiceForm()
    return render(request, 'indice/agregar.html', {'form': form})



@login_required
def editar_indice(request, pk):
    registro = get_object_or_404(RegistroIndice, pk=pk)
    if request.method == 'POST':
        form = RegistroIndiceForm(request.POST, instance=registro)
        if form.is_valid():
            instancia = form.save(commit=False)
            instancia.modificado_por = request.user  # Aquí asignamos automáticamente
            instancia.save()
            return redirect('lista_indices')
    else:
        form = RegistroIndiceForm(instance=registro)
    return render(request, 'indice/editar.html', {'form': form, 'registro': registro})


@login_required
def reportes_indices(request):
    """
    Vista para mostrar el formulario de generación de reportes
    """
    if request.method == 'POST':
        form = ReporteIndicesForm(request.POST)
        if form.is_valid():
            fecha_inicio = form.cleaned_data['fecha_inicio']
            fecha_fin = form.cleaned_data['fecha_fin']

            try:
                # Generar y descargar el PDF
                return generar_reporte_indices_pdf(fecha_inicio, fecha_fin)
            except Exception as e:
                messages.error(request, f'Error al generar el reporte: {str(e)}')
                return render(request, 'indice/reportes.html', {'form': form})
    else:
        form = ReporteIndicesForm()

    # Obtener estadísticas básicas para mostrar en la página
    total_registros = RegistroIndice.objects.count()

    context = {
        'form': form,
        'total_registros': total_registros,
    }

    return render(request, 'indice/reportes.html', context)


