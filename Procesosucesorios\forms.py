from django import forms
from .models import ProcesoSucesorio, EtapaProcesoSucesorio, ESTADOS_PROCESO_SUCESORIO, TIPOS_PROCESO

class ProcesoSucesorioForm(forms.ModelForm):
    """
    Formulario principal para crear y editar procesos sucesorios
    """
    
    class Meta:
        model = ProcesoSucesorio
        fields = [
            'numero_expediente', 'tipo_proceso', 'causante', 'solicitante',
            'fecha_fallecimiento', 'lugar_fallecimiento', 'bienes_descripcion',
            'valor_estimado_herencia', 'notario_nombre', 'notario_colegiado'
        ]
        
        widgets = {
            'numero_expediente': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ej: PS-001-2025',
                'required': True
            }),
            'tipo_proceso': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'causante': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre completo del fallecido',
                'required': True
            }),
            'solicitante': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre del heredero solicitante',
                'required': True
            }),
            'fecha_fallecimiento': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': True
            }),
            'lugar_fallecimiento': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ciudad, Departamento',
                'required': True
            }),
            'bienes_descripcion': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Descripción general de los bienes que conforman la herencia...'
            }),
            'valor_estimado_herencia': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'notario_nombre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre completo del notario (si aplica)'
            }),
            'notario_colegiado': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Número de colegiado'
            }),
        }

class EtapaProcesoSucesorioForm(forms.ModelForm):
    """
    Formulario genérico para actualizar cualquier etapa del proceso sucesorio
    """
    
    class Meta:
        model = EtapaProcesoSucesorio
        fields = ['fecha', 'observaciones', 'documento_principal', 'documento_adicional_1', 'documento_adicional_2']
        
        widgets = {
            'fecha': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'observaciones': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Observaciones y notas sobre esta etapa...'
            }),
            'documento_principal': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'documento_adicional_1': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'documento_adicional_2': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hacer que la fecha sea requerida
        self.fields['fecha'].required = True
        
        # Personalizar labels y placeholders según el tipo de etapa
        if self.instance and self.instance.tipo:
            etapa_nombre = dict(ESTADOS_PROCESO_SUCESORIO).get(self.instance.tipo, '')
            self.fields['observaciones'].widget.attrs['placeholder'] = f'Observaciones de {etapa_nombre.lower()}...'
            
            # Obtener información de documentos requeridos
            docs_info = self.instance.get_documentos_requeridos()
            
            # Personalizar labels de documentos
            if docs_info.get('principal'):
                self.fields['documento_principal'].label = docs_info['principal']
                self.fields['documento_principal'].help_text = f"Subir: {docs_info['principal']}"
            
            if docs_info.get('adicional_1'):
                self.fields['documento_adicional_1'].label = docs_info['adicional_1']
                self.fields['documento_adicional_1'].help_text = f"Subir: {docs_info['adicional_1']}"
            else:
                # Ocultar el campo si no es necesario
                self.fields['documento_adicional_1'].widget = forms.HiddenInput()
                self.fields['documento_adicional_1'].required = False
            
            if docs_info.get('adicional_2'):
                self.fields['documento_adicional_2'].label = docs_info['adicional_2']
                self.fields['documento_adicional_2'].help_text = f"Subir: {docs_info['adicional_2']}"
            else:
                # Ocultar el campo si no es necesario
                self.fields['documento_adicional_2'].widget = forms.HiddenInput()
                self.fields['documento_adicional_2'].required = False

# Formularios específicos para etapas que requieren campos adicionales especiales
class JuntaHerederosForm(EtapaProcesoSucesorioForm):
    """
    Formulario específico para la junta de herederos
    """
    fecha_junta = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label="Fecha de la Junta de Herederos"
    )
    
    lugar_junta = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Lugar donde se realizó la junta'
        }),
        label="Lugar de la Junta"
    )
    
    herederos_presentes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Lista de herederos que asistieron a la junta...'
        }),
        label="Herederos Presentes"
    )

class AvaluoForm(EtapaProcesoSucesorioForm):
    """
    Formulario específico para avalúos
    """
    valor_avaluo_inmuebles = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        }),
        label="Valor Avalúo Inmuebles (Q)"
    )
    
    valor_avaluo_muebles = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        }),
        label="Valor Avalúo Muebles (Q)"
    )
    
    perito_avaluador = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nombre del perito avaluador'
        }),
        label="Perito Avaluador"
    )

class PagoImpuestoForm(EtapaProcesoSucesorioForm):
    """
    Formulario específico para pago de impuestos
    """
    monto_impuesto = forms.DecimalField(
        required=False,
        max_digits=15,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        }),
        label="Monto del Impuesto (Q)"
    )
    
    numero_recibo = forms.CharField(
        required=False,
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Número de recibo de pago'
        }),
        label="Número de Recibo"
    )
    
    fecha_pago = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label="Fecha de Pago"
    )
