from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator

# Estados del proceso sucesorio - Basado en investigación de procesos sucesorios en Guatemala
ESTADOS_PROCESO_SUCESORIO = [
    ('acta_radicacion', '1. Acta de radicación'),
    ('resolucion', '2. Resolución'),
    ('notificacion', '3. Notificación'),
    ('edicto', '4. Edicto'),
    ('publicaciones', '5. Publicaciones'),
    ('aviso_csj', '6. Aviso CSJ'),
    ('informe_rgp', '7. Informe RGP'),
    ('informe_segundo_registro', '8. Informe 2º Registro'),
    ('junta_herederos', '9. Junta de Herederos'),
    ('acuse_recibido_csj', '10. Acuse recibido CSJ'),
    ('acuse_recibido_registros', '11. Acuse de recibido REGISTROS'),
    ('avaluo', '12. <PERSON><PERSON><PERSON><PERSON>(s)'),
    ('inventario', '13. Inventario'),
    ('pasar_pgn', '14. <PERSON>sar a PGN'),
    ('dictamen_favorable_pgn', '15. Dictamen favorable PGN'),
    ('auto_declaratoria_herederos', '16. Auto declaratoria de Herederos'),
    ('dicabi', '17. DICABI'),
    ('pago_impuesto_hereditario', '18. Pago impuesto hereditario'),
    ('rgp_sat', '19. RGP / SAT'),
    ('agt', '20. AGT'),
    ('finalizado', '21. FINALIZADO'),
]

# Tipos de proceso sucesorio
TIPOS_PROCESO = [
    ('intestado', 'Intestado'),
    ('testamentario', 'Testamentario'),
]

class ProcesoSucesorio(models.Model):
    """
    Modelo principal para el proceso sucesorio en Guatemala.
    Contiene la información general del expediente sucesorio.
    """

    # Información general del expediente
    numero_expediente = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="Número de Expediente",
        help_text="Ej: PS-001-2025"
    )
    tipo_proceso = models.CharField(
        max_length=20,
        choices=TIPOS_PROCESO,
        default='intestado',
        verbose_name="Tipo de Proceso"
    )
    causante = models.CharField(
        max_length=200,
        verbose_name="Nombre del Causante (Fallecido)",
        help_text="Nombre completo de la persona fallecida"
    )
    solicitante = models.CharField(
        max_length=200,
        verbose_name="Nombre del Solicitante",
        help_text="Heredero o representante que solicita el proceso"
    )
    fecha_fallecimiento = models.DateField(
        verbose_name="Fecha de Fallecimiento",
        help_text="Fecha de defunción del causante"
    )
    lugar_fallecimiento = models.CharField(
        max_length=200,
        verbose_name="Lugar de Fallecimiento",
        help_text="Ciudad y departamento donde falleció"
    )
    bienes_descripcion = models.TextField(
        verbose_name="Descripción de Bienes",
        help_text="Descripción general de los bienes que conforman la herencia",
        blank=True, null=True
    )
    valor_estimado_herencia = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name="Valor Estimado de la Herencia",
        help_text="Valor aproximado en quetzales de todos los bienes",
        blank=True, null=True
    )

    # Información del notario (si es extrajudicial)
    notario_nombre = models.CharField(
        max_length=200,
        verbose_name="Nombre del Notario",
        blank=True, null=True,
        help_text="Notario que lleva el proceso extrajudicial"
    )
    notario_colegiado = models.CharField(
        max_length=20,
        verbose_name="Número de Colegiado",
        blank=True, null=True,
        help_text="Número de colegiado del notario"
    )

    # Metadatos
    creado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='procesos_sucesorios_creados',
        verbose_name="Creado por"
    )
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación"
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización"
    )

    class Meta:
        verbose_name = "Proceso Sucesorio"
        verbose_name_plural = "Procesos Sucesorios"
        ordering = ['-fecha_creacion']

    def __str__(self):
        return f"{self.numero_expediente} - {self.causante}"

    def get_estado_actual(self):
        """Obtiene el estado actual basado en la última etapa completada"""
        ultima_etapa = self.etapas.filter(completada=True).order_by('-orden').first()
        if ultima_etapa:
            return ultima_etapa.tipo
        return 'acta_radicacion'

    def get_estado_display_numero(self):
        """Retorna el número de la etapa actual"""
        estado_actual = self.get_estado_actual()
        estados_numerados = dict(ESTADOS_PROCESO_SUCESORIO)
        return estados_numerados.get(estado_actual, estado_actual)

    def get_progreso_porcentaje(self):
        """Calcula el porcentaje de progreso basado en las etapas completadas"""
        total_etapas = len(ESTADOS_PROCESO_SUCESORIO)
        etapas_completadas = self.etapas.filter(completada=True).count()
        return round((etapas_completadas / total_etapas) * 100, 1)

    def get_siguiente_etapa(self):
        """Obtiene la siguiente etapa que debe completarse"""
        # Obtener todas las etapas ordenadas
        etapas_ordenadas = [estado[0] for estado in ESTADOS_PROCESO_SUCESORIO]

        # Encontrar etapas completadas
        etapas_completadas = set(
            self.etapas.filter(completada=True).values_list('tipo', flat=True)
        )

        # Encontrar la primera etapa no completada
        for etapa in etapas_ordenadas:
            if etapa not in etapas_completadas:
                return etapa

        # Si todas las etapas están completadas
        return 'finalizado'

    def inicializar_etapas(self):
        """Crea todas las etapas para este proceso sucesorio"""
        for orden, (tipo, _) in enumerate(ESTADOS_PROCESO_SUCESORIO, 1):
            EtapaProcesoSucesorio.objects.get_or_create(
                proceso=self,
                tipo=tipo,
                defaults={
                    'orden': orden,
                    'completada': False
                }
            )


class EtapaProcesoSucesorio(models.Model):
    """
    Modelo para representar cada etapa individual del proceso sucesorio.
    Cada etapa puede tener documentos específicos según su naturaleza.
    """
    proceso = models.ForeignKey(
        ProcesoSucesorio,
        on_delete=models.CASCADE,
        related_name='etapas',
        verbose_name="Proceso Sucesorio"
    )
    tipo = models.CharField(
        max_length=30,
        choices=ESTADOS_PROCESO_SUCESORIO,
        verbose_name="Tipo de Etapa"
    )
    orden = models.PositiveIntegerField(
        verbose_name="Orden de la Etapa",
        help_text="Orden secuencial de la etapa (1-21)"
    )
    fecha = models.DateField(
        null=True, blank=True,
        verbose_name="Fecha de Completación"
    )
    observaciones = models.TextField(
        blank=True, null=True,
        verbose_name="Observaciones",
        help_text="Notas y observaciones sobre esta etapa"
    )

    # Documentos principales (todos los tipos de etapa pueden tener estos)
    documento_principal = models.FileField(
        upload_to='procesos_sucesorios/',
        null=True, blank=True,
        verbose_name="Documento Principal",
        help_text="Documento principal de esta etapa (PDF o Word)"
    )

    # Documentos específicos según el tipo de etapa
    # Para etapas que requieren documentación adicional específica
    documento_adicional_1 = models.FileField(
        upload_to='procesos_sucesorios/',
        null=True, blank=True,
        verbose_name="Documento Adicional 1",
        help_text="Documento adicional específico de la etapa"
    )
    documento_adicional_2 = models.FileField(
        upload_to='procesos_sucesorios/',
        null=True, blank=True,
        verbose_name="Documento Adicional 2",
        help_text="Segundo documento adicional si es necesario"
    )

    completada = models.BooleanField(
        default=False,
        verbose_name="Etapa Completada"
    )
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación"
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización"
    )

    class Meta:
        verbose_name = "Etapa de Proceso Sucesorio"
        verbose_name_plural = "Etapas de Procesos Sucesorios"
        unique_together = ['proceso', 'tipo']
        ordering = ['orden']

    def __str__(self):
        estado_nombre = dict(ESTADOS_PROCESO_SUCESORIO).get(self.tipo, self.tipo)
        return f"{self.proceso.numero_expediente} - {estado_nombre}"

    def save(self, *args, **kwargs):
        # Marcar como completada si tiene fecha
        if self.fecha and not self.completada:
            self.completada = True
        elif not self.fecha and self.completada:
            self.completada = False
        super().save(*args, **kwargs)

    def get_nombre_etapa(self):
        """Obtiene el nombre completo de la etapa"""
        return dict(ESTADOS_PROCESO_SUCESORIO).get(self.tipo, self.tipo)

    def get_documentos_requeridos(self):
        """Retorna información sobre los documentos específicos requeridos para esta etapa"""
        documentos_info = {
            'acta_radicacion': {
                'principal': 'Acta notarial de radicación del proceso sucesorio',
                'adicional_1': 'Partida de defunción del causante',
                'adicional_2': 'Documentos de identidad de herederos'
            },
            'resolucion': {
                'principal': 'Resolución de radicación del proceso',
                'adicional_1': 'Notificaciones a herederos',
                'adicional_2': None
            },
            'edicto': {
                'principal': 'Edicto para publicación',
                'adicional_1': 'Comprobante de solicitud de publicación',
                'adicional_2': None
            },
            'publicaciones': {
                'principal': 'Publicaciones en Diario Oficial',
                'adicional_1': 'Publicaciones en diario de mayor circulación',
                'adicional_2': 'Comprobantes de pago de publicaciones'
            },
            'avaluo': {
                'principal': 'Avalúo de bienes inmuebles',
                'adicional_1': 'Avalúo de bienes muebles',
                'adicional_2': 'Documentos de propiedad de bienes'
            },
            'inventario': {
                'principal': 'Acta notarial de inventario',
                'adicional_1': 'Listado detallado de bienes',
                'adicional_2': 'Documentos de respaldo de bienes'
            },
            'dictamen_favorable_pgn': {
                'principal': 'Dictamen favorable de la PGN',
                'adicional_1': 'Memorial de solicitud a PGN',
                'adicional_2': None
            },
            'auto_declaratoria_herederos': {
                'principal': 'Auto de declaratoria de herederos',
                'adicional_1': 'Certificación del auto',
                'adicional_2': None
            },
            'dicabi': {
                'principal': 'Liquidación fiscal de DICABI',
                'adicional_1': 'Comprobante de pago de impuestos',
                'adicional_2': None
            },
            'pago_impuesto_hereditario': {
                'principal': 'Comprobante de pago de impuesto hereditario',
                'adicional_1': 'Liquidación del impuesto',
                'adicional_2': None
            }
        }

        return documentos_info.get(self.tipo, {
            'principal': 'Documento principal de la etapa',
            'adicional_1': None,
            'adicional_2': None
        })
