{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {{ titulo_pagina }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- Información del proceso -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="alert-heading mb-2">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    {{ proceso.numero_expediente }} - {{ proceso.causante }}
                                </h6>
                                <p class="mb-1">
                                    <i class="fas fa-user me-1"></i>
                                    Solicitante: {{ proceso.solicitante }}
                                </p>
                                <p class="mb-0">
                                    <span class="badge bg-{% if proceso.tipo_proceso == 'intestado' %}info{% else %}warning{% endif %}">
                                        {{ proceso.get_tipo_proceso_display }}
                                    </span>
                                    <i class="fas fa-calendar-alt ms-2 me-1"></i>
                                    Fallecimiento: {{ proceso.fecha_fallecimiento|date:"d/m/Y" }}
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ proceso.get_progreso_porcentaje }}%"
                                         aria-valuenow="{{ proceso.get_progreso_porcentaje }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ proceso.get_progreso_porcentaje }}%
                                    </div>
                                </div>
                                <small class="text-muted">Progreso del proceso</small>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Campo de fecha -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.fecha.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        {{ form.fecha.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.fecha }}
                                    {% if form.fecha.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.fecha.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Campos adicionales específicos por etapa -->
                            {% if etapa == 'junta_herederos' %}
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.fecha_junta.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ form.fecha_junta.label }}
                                        </label>
                                        {{ form.fecha_junta }}
                                        {% if form.fecha_junta.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.fecha_junta.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.lugar_junta.id_for_label }}" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ form.lugar_junta.label }}
                                        </label>
                                        {{ form.lugar_junta }}
                                        {% if form.lugar_junta.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.lugar_junta.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% elif etapa == 'avaluo' %}
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.valor_avaluo_inmuebles.id_for_label }}" class="form-label">
                                            <i class="fas fa-home me-1"></i>
                                            {{ form.valor_avaluo_inmuebles.label }}
                                        </label>
                                        {{ form.valor_avaluo_inmuebles }}
                                        {% if form.valor_avaluo_inmuebles.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.valor_avaluo_inmuebles.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.valor_avaluo_muebles.id_for_label }}" class="form-label">
                                            <i class="fas fa-couch me-1"></i>
                                            {{ form.valor_avaluo_muebles.label }}
                                        </label>
                                        {{ form.valor_avaluo_muebles }}
                                        {% if form.valor_avaluo_muebles.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.valor_avaluo_muebles.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.perito_avaluador.id_for_label }}" class="form-label">
                                            <i class="fas fa-user-tie me-1"></i>
                                            {{ form.perito_avaluador.label }}
                                        </label>
                                        {{ form.perito_avaluador }}
                                        {% if form.perito_avaluador.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.perito_avaluador.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% elif etapa == 'pago_impuesto_hereditario' or etapa == 'dicabi' %}
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.monto_impuesto.id_for_label }}" class="form-label">
                                            <i class="fas fa-dollar-sign me-1"></i>
                                            {{ form.monto_impuesto.label }}
                                        </label>
                                        {{ form.monto_impuesto }}
                                        {% if form.monto_impuesto.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.monto_impuesto.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.numero_recibo.id_for_label }}" class="form-label">
                                            <i class="fas fa-receipt me-1"></i>
                                            {{ form.numero_recibo.label }}
                                        </label>
                                        {{ form.numero_recibo }}
                                        {% if form.numero_recibo.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.numero_recibo.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.fecha_pago.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-check me-1"></i>
                                            {{ form.fecha_pago.label }}
                                        </label>
                                        {{ form.fecha_pago }}
                                        {% if form.fecha_pago.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.fecha_pago.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Campo de observaciones -->
                        <div class="mb-3">
                            <label for="{{ form.observaciones.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                {{ form.observaciones.label }}
                            </label>
                            {{ form.observaciones }}
                            {% if form.observaciones.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.observaciones.errors.0 }}
                                </div>
                            {% endif %}
                            
                            {% if etapa == 'junta_herederos' %}
                                <div class="mt-2">
                                    <label for="{{ form.herederos_presentes.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>
                                        {{ form.herederos_presentes.label }}
                                    </label>
                                    {{ form.herederos_presentes }}
                                    {% if form.herederos_presentes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.herederos_presentes.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Campos de documentos -->
                        <div class="row">
                            <!-- Documento principal -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.documento_principal.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-upload me-1"></i>
                                        {{ form.documento_principal.label }}
                                    </label>
                                    {{ form.documento_principal }}
                                    {% if form.documento_principal.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.documento_principal.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.documento_principal.help_text %}
                                        <div class="form-text">
                                            {{ form.documento_principal.help_text }}
                                        </div>
                                    {% endif %}
                                    
                                    <!-- Mostrar documento actual si existe -->
                                    {% if etapa_obj.documento_principal %}
                                        <div class="mt-2">
                                            <div class="alert alert-info">
                                                <i class="fas fa-file me-1"></i>
                                                <strong>Documento actual:</strong>
                                                <a href="{{ etapa_obj.documento_principal.url }}" target="_blank" class="alert-link">
                                                    Ver documento
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Documento adicional 1 -->
                            {% if form.documento_adicional_1.widget.input_type != 'hidden' %}
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.documento_adicional_1.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-upload me-1"></i>
                                        {{ form.documento_adicional_1.label }}
                                    </label>
                                    {{ form.documento_adicional_1 }}
                                    {% if form.documento_adicional_1.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.documento_adicional_1.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.documento_adicional_1.help_text %}
                                        <div class="form-text">
                                            {{ form.documento_adicional_1.help_text }}
                                        </div>
                                    {% endif %}
                                    
                                    {% if etapa_obj.documento_adicional_1 %}
                                        <div class="mt-2">
                                            <div class="alert alert-info">
                                                <i class="fas fa-file me-1"></i>
                                                <strong>Documento actual:</strong>
                                                <a href="{{ etapa_obj.documento_adicional_1.url }}" target="_blank" class="alert-link">
                                                    Ver documento
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                            
                            <!-- Documento adicional 2 -->
                            {% if form.documento_adicional_2.widget.input_type != 'hidden' %}
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.documento_adicional_2.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-upload me-1"></i>
                                        {{ form.documento_adicional_2.label }}
                                    </label>
                                    {{ form.documento_adicional_2 }}
                                    {% if form.documento_adicional_2.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.documento_adicional_2.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.documento_adicional_2.help_text %}
                                        <div class="form-text">
                                            {{ form.documento_adicional_2.help_text }}
                                        </div>
                                    {% endif %}
                                    
                                    {% if etapa_obj.documento_adicional_2 %}
                                        <div class="mt-2">
                                            <div class="alert alert-info">
                                                <i class="fas fa-file me-1"></i>
                                                <strong>Documento actual:</strong>
                                                <a href="{{ etapa_obj.documento_adicional_2.url }}" target="_blank" class="alert-link">
                                                    Ver documento
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Información sobre la etapa -->
                        <div class="alert alert-light border">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-info-circle me-1"></i>
                                Información sobre esta etapa
                            </h6>
                            <p class="mb-0 small">
                                {% if etapa == 'acta_radicacion' %}
                                    El acta de radicación es el documento inicial que da inicio formal al proceso sucesorio.
                                    Debe incluir la partida de defunción y documentos de identidad de los herederos.
                                {% elif etapa == 'resolucion' %}
                                    La resolución establece los términos y condiciones del proceso sucesorio, incluyendo
                                    las notificaciones que deben realizarse.
                                {% elif etapa == 'junta_herederos' %}
                                    En la junta de herederos se reúnen todos los herederos para acordar la distribución
                                    de los bienes y designar representantes si es necesario.
                                {% elif etapa == 'avaluo' %}
                                    El avalúo determina el valor comercial de todos los bienes que conforman la herencia,
                                    tanto inmuebles como muebles, realizado por peritos especializados.
                                {% elif etapa == 'inventario' %}
                                    El inventario es la relación detallada de todos los bienes, derechos y obligaciones
                                    que conforman el patrimonio del causante.
                                {% elif etapa == 'dictamen_favorable_pgn' %}
                                    El dictamen de la Procuraduría General de la Nación es necesario para verificar
                                    que el proceso cumple con todos los requisitos legales.
                                {% elif etapa == 'auto_declaratoria_herederos' %}
                                    El auto de declaratoria de herederos es la resolución judicial que reconoce
                                    oficialmente a los herederos del causante.
                                {% elif etapa == 'dicabi' %}
                                    DICABI realiza la liquidación fiscal para determinar los impuestos que deben
                                    pagarse sobre la herencia.
                                {% elif etapa == 'pago_impuesto_hereditario' %}
                                    El pago del impuesto hereditario debe realizarse antes de proceder con el registro
                                    de los bienes a nombre de los herederos.
                                {% elif etapa == 'finalizado' %}
                                    El proceso ha sido completado exitosamente. Todos los bienes han sido registrados
                                    a nombre de los herederos correspondientes.
                                {% else %}
                                    Esta etapa es parte del proceso sucesorio y debe completarse según los requisitos
                                    establecidos por la ley guatemalteca.
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'detalle_proceso_sucesorio' proceso.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Volver al Detalle
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Actualizar Etapa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar fecha actual por defecto si el campo está vacío
    const fechaInput = document.querySelector('input[type="date"][name="fecha"]');
    if (fechaInput && !fechaInput.value) {
        const today = new Date().toISOString().split('T')[0];
        fechaInput.value = today;
    }
    
    // Validación en tiempo real
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>

{% endblock %}
