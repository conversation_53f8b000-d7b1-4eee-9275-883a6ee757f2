{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        {{ titulo }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="POST" novalidate>
                        {% csrf_token %}
                        
                        <!-- Información básica del expediente -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Información Básica del Expediente
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.numero_expediente.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i>
                                        {{ form.numero_expediente.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.numero_expediente }}
                                    {% if form.numero_expediente.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.numero_expediente.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.numero_expediente.help_text %}
                                        <div class="form-text">{{ form.numero_expediente.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.tipo_proceso.id_for_label }}" class="form-label">
                                        <i class="fas fa-balance-scale me-1"></i>
                                        {{ form.tipo_proceso.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.tipo_proceso }}
                                    {% if form.tipo_proceso.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tipo_proceso.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Información del causante y solicitante -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-users me-2"></i>
                                    Información de Personas
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.causante.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        {{ form.causante.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.causante }}
                                    {% if form.causante.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.causante.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.causante.help_text %}
                                        <div class="form-text">{{ form.causante.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.solicitante.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>
                                        {{ form.solicitante.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.solicitante }}
                                    {% if form.solicitante.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.solicitante.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.solicitante.help_text %}
                                        <div class="form-text">{{ form.solicitante.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.fecha_fallecimiento.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        {{ form.fecha_fallecimiento.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.fecha_fallecimiento }}
                                    {% if form.fecha_fallecimiento.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.fecha_fallecimiento.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.fecha_fallecimiento.help_text %}
                                        <div class="form-text">{{ form.fecha_fallecimiento.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.lugar_fallecimiento.id_for_label }}" class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ form.lugar_fallecimiento.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.lugar_fallecimiento }}
                                    {% if form.lugar_fallecimiento.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.lugar_fallecimiento.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.lugar_fallecimiento.help_text %}
                                        <div class="form-text">{{ form.lugar_fallecimiento.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Información de bienes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-home me-2"></i>
                                    Información de Bienes
                                </h5>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.bienes_descripcion.id_for_label }}" class="form-label">
                                        <i class="fas fa-list me-1"></i>
                                        {{ form.bienes_descripcion.label }}
                                    </label>
                                    {{ form.bienes_descripcion }}
                                    {% if form.bienes_descripcion.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.bienes_descripcion.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.bienes_descripcion.help_text %}
                                        <div class="form-text">{{ form.bienes_descripcion.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.valor_estimado_herencia.id_for_label }}" class="form-label">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        {{ form.valor_estimado_herencia.label }}
                                    </label>
                                    {{ form.valor_estimado_herencia }}
                                    {% if form.valor_estimado_herencia.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.valor_estimado_herencia.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.valor_estimado_herencia.help_text %}
                                        <div class="form-text">{{ form.valor_estimado_herencia.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Información del notario -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-user-graduate me-2"></i>
                                    Información del Notario (Opcional)
                                </h5>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.notario_nombre.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>
                                        {{ form.notario_nombre.label }}
                                    </label>
                                    {{ form.notario_nombre }}
                                    {% if form.notario_nombre.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.notario_nombre.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.notario_nombre.help_text %}
                                        <div class="form-text">{{ form.notario_nombre.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.notario_colegiado.id_for_label }}" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>
                                        {{ form.notario_colegiado.label }}
                                    </label>
                                    {{ form.notario_colegiado }}
                                    {% if form.notario_colegiado.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.notario_colegiado.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.notario_colegiado.help_text %}
                                        <div class="form-text">{{ form.notario_colegiado.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'lista_procesos_sucesorios' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Crear Proceso Sucesorio
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación en tiempo real
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
    
    // Formatear valor estimado
    const valorInput = document.querySelector('input[name="valor_estimado_herencia"]');
    if (valorInput) {
        valorInput.addEventListener('input', function() {
            // Remover caracteres no numéricos excepto punto decimal
            this.value = this.value.replace(/[^0-9.]/g, '');
        });
    }
});
</script>

{% endblock %}
