{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header del expediente -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="card-title mb-1">
                            <i class="fas fa-balance-scale me-2"></i>
                            {{ proceso.numero_expediente }}
                        </h4>
                        <p class="text-muted mb-0">
                            <span class="badge bg-{% if proceso.tipo_proceso == 'intestado' %}info{% else %}warning{% endif %} me-2">
                                {{ proceso.get_tipo_proceso_display }}
                            </span>
                            Causante: {{ proceso.causante }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group" role="group">
                            <a href="{% url 'editar_proceso_sucesorio' proceso.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>
                                Editar
                            </a>
                            <a href="{% url 'avanzar_etapa_proceso_sucesorio' proceso.pk %}" class="btn btn-success">
                                <i class="fas fa-step-forward me-1"></i>
                                Siguiente Etapa
                            </a>
                            <a href="{% url 'lista_procesos_sucesorios' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Volver
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user me-1"></i> Solicitante:</h6>
                                    <p class="text-muted">{{ proceso.solicitante }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-calendar-alt me-1"></i> Fecha de Fallecimiento:</h6>
                                    <p class="text-muted">{{ proceso.fecha_fallecimiento|date:"d/m/Y" }}</p>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-map-marker-alt me-1"></i> Lugar de Fallecimiento:</h6>
                                    <p class="text-muted">{{ proceso.lugar_fallecimiento }}</p>
                                </div>
                                {% if proceso.valor_estimado_herencia %}
                                <div class="col-md-6">
                                    <h6><i class="fas fa-dollar-sign me-1"></i> Valor Estimado:</h6>
                                    <p class="text-muted">Q{{ proceso.valor_estimado_herencia|floatformat:2 }}</p>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if proceso.bienes_descripcion %}
                            <div class="row">
                                <div class="col-12">
                                    <h6><i class="fas fa-list me-1"></i> Descripción de Bienes:</h6>
                                    <p class="text-muted">{{ proceso.bienes_descripcion }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if proceso.notario_nombre %}
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user-tie me-1"></i> Notario:</h6>
                                    <p class="text-muted">{{ proceso.notario_nombre }}</p>
                                </div>
                                {% if proceso.notario_colegiado %}
                                <div class="col-md-6">
                                    <h6><i class="fas fa-id-card me-1"></i> Colegiado:</h6>
                                    <p class="text-muted">{{ proceso.notario_colegiado }}</p>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>Progreso del Proceso</h6>
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: {{ progreso_porcentaje }}%"
                                         aria-valuenow="{{ progreso_porcentaje }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ progreso_porcentaje }}%
                                    </div>
                                </div>
                                <p class="text-muted small">
                                    Estado actual: <strong>{{ proceso.get_estado_display_numero }}</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Timeline del proceso -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-timeline me-2"></i>
                        Proceso Sucesorio - {{ proceso.get_tipo_proceso_display }}
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="timeline">
                        {% for etapa in etapas %}
                            <div class="timeline-item {% if etapa.completada %}completed{% elif etapa.tipo == proceso.get_siguiente_etapa %}active{% endif %}">
                                <div class="timeline-marker">
                                    {% if etapa.tipo == 'acta_radicacion' %}
                                        <i class="fas fa-file-signature"></i>
                                    {% elif etapa.tipo == 'resolucion' %}
                                        <i class="fas fa-gavel"></i>
                                    {% elif etapa.tipo == 'notificacion' %}
                                        <i class="fas fa-bell"></i>
                                    {% elif etapa.tipo == 'edicto' %}
                                        <i class="fas fa-newspaper"></i>
                                    {% elif etapa.tipo == 'publicaciones' %}
                                        <i class="fas fa-bullhorn"></i>
                                    {% elif etapa.tipo == 'aviso_csj' %}
                                        <i class="fas fa-share"></i>
                                    {% elif etapa.tipo == 'informe_rgp' %}
                                        <i class="fas fa-file-alt"></i>
                                    {% elif etapa.tipo == 'informe_segundo_registro' %}
                                        <i class="fas fa-file-alt"></i>
                                    {% elif etapa.tipo == 'junta_herederos' %}
                                        <i class="fas fa-users"></i>
                                    {% elif etapa.tipo == 'acuse_recibido_csj' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif etapa.tipo == 'acuse_recibido_registros' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif etapa.tipo == 'avaluo' %}
                                        <i class="fas fa-calculator"></i>
                                    {% elif etapa.tipo == 'inventario' %}
                                        <i class="fas fa-clipboard-list"></i>
                                    {% elif etapa.tipo == 'pasar_pgn' %}
                                        <i class="fas fa-share"></i>
                                    {% elif etapa.tipo == 'dictamen_favorable_pgn' %}
                                        <i class="fas fa-thumbs-up"></i>
                                    {% elif etapa.tipo == 'auto_declaratoria_herederos' %}
                                        <i class="fas fa-stamp"></i>
                                    {% elif etapa.tipo == 'dicabi' %}
                                        <i class="fas fa-building"></i>
                                    {% elif etapa.tipo == 'pago_impuesto_hereditario' %}
                                        <i class="fas fa-money-bill"></i>
                                    {% elif etapa.tipo == 'rgp_sat' %}
                                        <i class="fas fa-file-invoice"></i>
                                    {% elif etapa.tipo == 'agt' %}
                                        <i class="fas fa-file-contract"></i>
                                    {% elif etapa.tipo == 'finalizado' %}
                                        <i class="fas fa-check"></i>
                                    {% endif %}
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="timeline-title">{{ etapa.get_nombre_etapa }}</h6>
                                            {% if etapa.fecha %}
                                                <p class="text-success mb-1">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    Completado: {{ etapa.fecha|date:"d/m/Y" }}
                                                </p>
                                            {% endif %}
                                            {% if etapa.observaciones %}
                                                <p class="text-muted small mb-2">{{ etapa.observaciones|truncatewords:20 }}</p>
                                            {% endif %}
                                            
                                            <!-- Mostrar documentos -->
                                            {% if etapa.documento_principal %}
                                                <p class="mb-1">
                                                    <i class="fas fa-file-pdf text-danger me-1"></i>
                                                    <a href="{{ etapa.documento_principal.url }}" target="_blank" class="text-decoration-none">
                                                        Documento principal
                                                    </a>
                                                </p>
                                            {% endif %}
                                            {% if etapa.documento_adicional_1 %}
                                                <p class="mb-1">
                                                    <i class="fas fa-file-pdf text-danger me-1"></i>
                                                    <a href="{{ etapa.documento_adicional_1.url }}" target="_blank" class="text-decoration-none">
                                                        Documento adicional 1
                                                    </a>
                                                </p>
                                            {% endif %}
                                            {% if etapa.documento_adicional_2 %}
                                                <p class="mb-1">
                                                    <i class="fas fa-file-pdf text-danger me-1"></i>
                                                    <a href="{{ etapa.documento_adicional_2.url }}" target="_blank" class="text-decoration-none">
                                                        Documento adicional 2
                                                    </a>
                                                </p>
                                            {% endif %}
                                        </div>
                                        <a href="{% url 'actualizar_etapa_proceso_sucesorio' proceso.pk etapa.tipo %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-item.active .timeline-marker {
    background: #007bff;
    animation: pulse 2s infinite;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}

.timeline-item.active .timeline-content {
    border-left-color: #007bff;
    background: #e3f2fd;
}

.timeline-item.completed .timeline-content {
    border-left-color: #28a745;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}
</style>

{% endblock %}
