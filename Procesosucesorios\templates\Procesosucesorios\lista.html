{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-balance-scale me-2 text-primary"></i>
                        Procesos Sucesorios
                    </h2>
                    <p class="text-muted mb-0">Gestión de procesos sucesorios intestados y testamentarios</p>
                </div>
                <div>
                    <a href="{% url 'crear_proceso_sucesorio' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Nuevo Proceso Sucesorio
                    </a>
                    
                </div>
            </div>

            <!-- Filtros y búsqueda -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="q" class="form-label">Buscar</label>
                            <input type="text" class="form-control" id="q" name="q" value="{{ query }}" 
                                   placeholder="Número, causante, solicitante, notario...">
                        </div>
                        <div class="col-md-3">
                            <label for="tipo" class="form-label">Tipo de Proceso</label>
                            <select class="form-control" id="tipo" name="tipo">
                                <option value="">Todos los tipos</option>
                                {% for valor, nombre in tipos_proceso %}
                                    <option value="{{ valor }}" {% if tipo_filtro == valor %}selected{% endif %}>
                                        {{ nombre }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="estado" class="form-label">Estado</label>
                            <select class="form-control" id="estado" name="estado">
                                <option value="">Todos los estados</option>
                                {% for valor, nombre in estados_proceso %}
                                    <option value="{{ valor }}" {% if estado_filtro == valor %}selected{% endif %}>
                                        {{ nombre }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i>
                            </button>
                            <a href="{% url 'lista_procesos_sucesorios' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Lista de procesos -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Procesos Sucesorios
                        {% if page_obj.paginator.count %}
                            <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                        {% endif %}
                    </h5>
                </div>
                
                <div class="card-body p-0">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Expediente</th>
                                        <th>Tipo</th>
                                        <th>Causante</th>
                                        <th>Solicitante</th>
                                        <th>Estado Actual</th>
                                        <th>Progreso</th>
                                        <th>Fecha Creación</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for proceso in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ proceso.numero_expediente }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-{% if proceso.tipo_proceso == 'intestado' %}info{% else %}warning{% endif %}">
                                                    {{ proceso.get_tipo_proceso_display }}
                                                </span>
                                            </td>
                                            <td>{{ proceso.causante|truncatechars:30 }}</td>
                                            <td>{{ proceso.solicitante|truncatechars:30 }}</td>
                                            <td>
                                                <small class="text-muted">{{ proceso.get_estado_display_numero|truncatechars:25 }}</small>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px; width: 100px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ proceso.get_progreso_porcentaje }}%"
                                                         aria-valuenow="{{ proceso.get_progreso_porcentaje }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        {{ proceso.get_progreso_porcentaje }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-muted">{{ proceso.fecha_creacion|date:"d/m/Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{% url 'detalle_proceso_sucesorio' proceso.pk %}" 
                                                       class="btn btn-outline-primary" title="Ver detalles">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'editar_proceso_sucesorio' proceso.pk %}" 
                                                       class="btn btn-outline-warning" title="Editar">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'avanzar_etapa_proceso_sucesorio' proceso.pk %}" 
                                                       class="btn btn-outline-success" title="Siguiente etapa">
                                                        <i class="fas fa-step-forward"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Paginación -->
                        {% if page_obj.has_other_pages %}
                            <div class="card-footer">
                                <nav aria-label="Paginación de procesos sucesorios">
                                    <ul class="pagination justify-content-center mb-0">
                                        {% if page_obj.has_previous %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}{% if tipo_filtro %}&tipo={{ tipo_filtro }}{% endif %}">
                                                    <i class="fas fa-angle-double-left"></i>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}{% if tipo_filtro %}&tipo={{ tipo_filtro }}{% endif %}">
                                                    <i class="fas fa-angle-left"></i>
                                                </a>
                                            </li>
                                        {% endif %}
                                        
                                        <li class="page-item active">
                                            <span class="page-link">
                                                Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                            </span>
                                        </li>
                                        
                                        {% if page_obj.has_next %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}{% if tipo_filtro %}&tipo={{ tipo_filtro }}{% endif %}">
                                                    <i class="fas fa-angle-right"></i>
                                                </a>
                                            </li>
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}{% if tipo_filtro %}&tipo={{ tipo_filtro }}{% endif %}">
                                                    <i class="fas fa-angle-double-right"></i>
                                                </a>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay procesos sucesorios</h5>
                            <p class="text-muted">Comienza creando tu primer proceso sucesorio.</p>
                            <a href="{% url 'crear_proceso_sucesorio' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Crear Proceso Sucesorio
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(45deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}
</style>

{% endblock %}
