from django.urls import path
from . import views

urlpatterns = [
    # Lista y dashboard
    path('', views.lista_procesos_sucesorios, name='lista_procesos_sucesorios'),
    path('dashboard/', views.dashboard_procesos_sucesorios, name='dashboard_procesos_sucesorios'),
    
    # CRUD de procesos sucesorios
    path('crear/', views.crear_proceso_sucesorio, name='crear_proceso_sucesorio'),
    path('<int:pk>/', views.detalle_proceso_sucesorio, name='detalle_proceso_sucesorio'),
    path('<int:pk>/editar/', views.editar_proceso_sucesorio, name='editar_proceso_sucesorio'),
    path('<int:pk>/eliminar/', views.eliminar_proceso_sucesorio, name='eliminar_proceso_sucesorio'),
    
    # Gestión de etapas
    path('<int:pk>/etapa/<str:etapa>/', views.actualizar_etapa_proceso_sucesorio, name='actualizar_etapa_proceso_sucesorio'),
    path('<int:pk>/avanzar/', views.avanzar_etapa_proceso_sucesorio, name='avanzar_etapa_proceso_sucesorio'),
]
