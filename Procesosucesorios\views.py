from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import ProcesoSucesorio, EtapaProcesoSucesorio, ESTADOS_PROCESO_SUCESORIO
from .forms import (
    ProcesoSucesorioForm, EtapaProcesoSucesorioForm,
    JuntaHerederosForm, AvaluoForm, PagoImpuestoForm
)

@login_required
def lista_procesos_sucesorios(request):
    """Vista para listar todos los procesos sucesorios"""

    # Búsqueda
    query = request.GET.get('q', '')
    estado_filtro = request.GET.get('estado', '')
    tipo_filtro = request.GET.get('tipo', '')

    procesos = ProcesoSucesorio.objects.all()

    if query:
        procesos = procesos.filter(
            Q(numero_expediente__icontains=query) |
            Q(causante__icontains=query) |
            Q(solicitante__icontains=query) |
            Q(lugar_fallecimiento__icontains=query) |
            Q(notario_nombre__icontains=query)
        )

    if estado_filtro:
        # Filtrar por procesos que tengan una etapa específica completada
        procesos = procesos.filter(etapas__tipo=estado_filtro, etapas__completada=True)

    if tipo_filtro:
        procesos = procesos.filter(tipo_proceso=tipo_filtro)

    # Paginación
    paginator = Paginator(procesos, 10)  # 10 procesos por página
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'query': query,
        'estado_filtro': estado_filtro,
        'tipo_filtro': tipo_filtro,
        'estados_proceso': ESTADOS_PROCESO_SUCESORIO,
        'tipos_proceso': [('intestado', 'Intestado'), ('testamentario', 'Testamentario')],
    }

    return render(request, 'Procesosucesorios/lista.html', context)

@login_required
def crear_proceso_sucesorio(request):
    """Vista para crear un nuevo proceso sucesorio"""

    if request.method == 'POST':
        form = ProcesoSucesorioForm(request.POST)
        if form.is_valid():
            proceso = form.save(commit=False)
            proceso.creado_por = request.user
            proceso.save()

            # Inicializar todas las etapas
            proceso.inicializar_etapas()

            messages.success(request, f'Proceso sucesorio {proceso.numero_expediente} creado exitosamente.')
            return redirect('detalle_proceso_sucesorio', pk=proceso.pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = ProcesoSucesorioForm()

    context = {
        'form': form,
        'titulo': 'Crear Nuevo Proceso Sucesorio'
    }

    return render(request, 'Procesosucesorios/crear.html', context)

@login_required
def detalle_proceso_sucesorio(request, pk):
    """Vista para ver los detalles de un proceso sucesorio"""

    proceso = get_object_or_404(ProcesoSucesorio, pk=pk)

    # Obtener todas las etapas ordenadas
    etapas = proceso.etapas.all().order_by('orden')

    context = {
        'proceso': proceso,
        'etapas': etapas,
        'progreso_porcentaje': proceso.get_progreso_porcentaje(),
    }

    return render(request, 'Procesosucesorios/detalle.html', context)

@login_required
def editar_proceso_sucesorio(request, pk):
    """Vista para editar la información general de un proceso sucesorio"""

    proceso = get_object_or_404(ProcesoSucesorio, pk=pk)

    if request.method == 'POST':
        form = ProcesoSucesorioForm(request.POST, instance=proceso)
        if form.is_valid():
            form.save()
            messages.success(request, f'Proceso sucesorio {proceso.numero_expediente} actualizado exitosamente.')
            return redirect('detalle_proceso_sucesorio', pk=proceso.pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = ProcesoSucesorioForm(instance=proceso)

    context = {
        'form': form,
        'proceso': proceso,
        'titulo_pagina': f'Editar {proceso.numero_expediente}'
    }

    return render(request, 'Procesosucesorios/editar.html', context)

@login_required
def actualizar_etapa_proceso_sucesorio(request, pk, etapa):
    """Vista para actualizar una etapa específica del proceso sucesorio"""

    proceso = get_object_or_404(ProcesoSucesorio, pk=pk)

    # Obtener o crear la etapa
    etapa_obj, created = EtapaProcesoSucesorio.objects.get_or_create(
        proceso=proceso,
        tipo=etapa,
        defaults={
            'orden': next((i for i, (key, _) in enumerate(ESTADOS_PROCESO_SUCESORIO, 1) if key == etapa), 1)
        }
    )

    # Nombres de etapas para mostrar
    nombres_etapas = dict(ESTADOS_PROCESO_SUCESORIO)

    if etapa not in [estado[0] for estado in ESTADOS_PROCESO_SUCESORIO]:
        messages.error(request, 'Etapa no válida.')
        return redirect('detalle_proceso_sucesorio', pk=pk)

    # Usar formulario específico según el tipo de etapa
    if etapa == 'junta_herederos':
        FormClass = JuntaHerederosForm
    elif etapa == 'avaluo':
        FormClass = AvaluoForm
    elif etapa in ['pago_impuesto_hereditario', 'dicabi']:
        FormClass = PagoImpuestoForm
    else:
        FormClass = EtapaProcesoSucesorioForm

    if request.method == 'POST':
        form = FormClass(request.POST, request.FILES, instance=etapa_obj)
        if form.is_valid():
            etapa_actualizada = form.save(commit=False)

            # Marcar como completada si tiene fecha
            if etapa_actualizada.fecha:
                etapa_actualizada.completada = True

            etapa_actualizada.save()

            messages.success(request, f'Etapa "{nombres_etapas[etapa]}" actualizada exitosamente.')
            return redirect('detalle_proceso_sucesorio', pk=pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = FormClass(instance=etapa_obj)

    context = {
        'form': form,
        'proceso': proceso,
        'etapa': etapa,
        'etapa_obj': etapa_obj,
        'nombre_etapa': nombres_etapas[etapa],
        'titulo_pagina': f'{nombres_etapas[etapa]} - {proceso.numero_expediente}'
    }

    return render(request, 'Procesosucesorios/actualizar_etapa.html', context)

@login_required
def eliminar_proceso_sucesorio(request, pk):
    """Vista para eliminar un proceso sucesorio"""

    proceso = get_object_or_404(ProcesoSucesorio, pk=pk)

    if request.method == 'POST':
        numero_expediente = proceso.numero_expediente
        proceso.delete()
        messages.success(request, f'Proceso sucesorio {numero_expediente} eliminado exitosamente.')
        return redirect('lista_procesos_sucesorios')

    context = {
        'proceso': proceso
    }

    return render(request, 'Procesosucesorios/eliminar.html', context)

@login_required
def dashboard_procesos_sucesorios(request):
    """Vista del dashboard con estadísticas de procesos sucesorios"""

    # Estadísticas generales
    total_procesos = ProcesoSucesorio.objects.count()
    procesos_finalizados = ProcesoSucesorio.objects.filter(
        etapas__tipo='finalizado',
        etapas__completada=True
    ).count()
    procesos_en_proceso = total_procesos - procesos_finalizados

    # Procesos por tipo
    procesos_intestados = ProcesoSucesorio.objects.filter(tipo_proceso='intestado').count()
    procesos_testamentarios = ProcesoSucesorio.objects.filter(tipo_proceso='testamentario').count()

    # Procesos por estado (basado en última etapa completada)
    estados_count = {}
    for proceso in ProcesoSucesorio.objects.all():
        estado_actual = proceso.get_estado_actual()
        estado_nombre = dict(ESTADOS_PROCESO_SUCESORIO).get(estado_actual, estado_actual)
        estados_count[estado_nombre] = estados_count.get(estado_nombre, 0) + 1

    # Procesos recientes
    procesos_recientes = ProcesoSucesorio.objects.order_by('-fecha_creacion')[:5]

    context = {
        'total_procesos': total_procesos,
        'procesos_finalizados': procesos_finalizados,
        'procesos_en_proceso': procesos_en_proceso,
        'procesos_intestados': procesos_intestados,
        'procesos_testamentarios': procesos_testamentarios,
        'estados_count': estados_count,
        'procesos_recientes': procesos_recientes,
    }

    return render(request, 'Procesosucesorios/dashboard.html', context)

@login_required
def avanzar_etapa_proceso_sucesorio(request, pk):
    """Vista para avanzar a la siguiente etapa - redirige al formulario correspondiente"""

    proceso = get_object_or_404(ProcesoSucesorio, pk=pk)
    siguiente_etapa = proceso.get_siguiente_etapa()

    if siguiente_etapa == 'finalizado':
        # Verificar si ya está finalizado
        etapa_finalizada = proceso.etapas.filter(tipo='finalizado', completada=True).first()
        if etapa_finalizada:
            messages.info(request, 'El proceso ya está completamente finalizado.')
            return redirect('detalle_proceso_sucesorio', pk=pk)

    # Redirigir al formulario de la siguiente etapa
    nombre_etapa = dict(ESTADOS_PROCESO_SUCESORIO).get(siguiente_etapa, siguiente_etapa)
    messages.info(request, f'Completando la siguiente etapa: {nombre_etapa}')
    return redirect('actualizar_etapa_proceso_sucesorio', pk=pk, etapa=siguiente_etapa)
