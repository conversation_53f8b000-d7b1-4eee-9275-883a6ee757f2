from django import forms
from .models import TituloSupletorio, EtapaTituloSupletorio, ESTADOS_PROCESO

class TituloSupletorioForm(forms.ModelForm):
    """
    Formulario principal para crear y editar títulos supletorios
    """
    
    class Meta:
        model = TituloSupletorio
        fields = [
            'numero_expediente', 'solicitante', 'ubicacion_inmueble', 
            'departamento', 'municipio'
        ]
        
        widgets = {
            'numero_expediente': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ej: TS-001-2025',
                'required': True
            }),
            'solicitante': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nombre completo del solicitante',
                'required': True
            }),
            'ubicacion_inmueble': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Descripción detallada de la ubicación del inmueble',
                'required': True
            }),
            'departamento': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Departamento',
                'required': True
            }),
            'municipio': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Municipio',
                'required': True
            }),
        }

class EtapaForm(forms.ModelForm):
    """
    Formulario genérico para actualizar cualquier etapa del proceso
    """
    
    class Meta:
        model = EtapaTituloSupletorio
        fields = ['fecha', 'observaciones', 'documento']
        
        widgets = {
            'fecha': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'observaciones': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Observaciones y notas sobre esta etapa...'
            }),
            'documento': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hacer que la fecha sea requerida
        self.fields['fecha'].required = True
        
        # Personalizar placeholder de observaciones según el tipo de etapa
        if self.instance and self.instance.tipo:
            etapa_nombre = dict(ESTADOS_PROCESO).get(self.instance.tipo, '')
            self.fields['observaciones'].widget.attrs['placeholder'] = f'Observaciones de {etapa_nombre.lower()}...'

# Formularios específicos para etapas que requieren campos adicionales
class PrimeraResolucionForm(EtapaForm):
    """
    Formulario específico para la primera resolución que incluye fechas de audiencias
    """
    fecha_audiencia_experto = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label="Fecha Audiencia Experto Medidor"
    )
    
    fecha_audiencia_testigos = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label="Fecha Audiencia Testigos"
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Personalizar placeholder
        self.fields['observaciones'].widget.attrs['placeholder'] = 'Observaciones de la primera resolución y fechas de audiencias...'
