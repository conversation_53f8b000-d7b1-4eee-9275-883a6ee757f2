from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator

# Estados del proceso - Movido fuera de la clase para reutilización
ESTADOS_PROCESO = [
    ('memorial_inicial', '1. Memorial inicial'),
    ('primera_resolucion', '2. Primera resolución (fechas de audiencias)'),
    ('notificacion_colindantes', '3. Notificación colindantes'),
    ('audiencia_testigos', '4. Audiencia Testigos'),
    ('audiencia_experto', '5. Audiencia Experto Medidor'),
    ('inspeccion_municipal', '6. Inspección Municipal'),
    ('informe_municipal', '7. Informe Municipal'),
    ('edictos', '8. Edictos'),
    ('publicaciones', '9. Publicaciones'),
    ('memorial_experto', '10. Memorial experto medidor'),
    ('memorial_publicaciones_pgn', '11. Memorial presentación de publicaciones y Remitir a PGN'),
    ('auto', '12. AUTO'),
    ('certificacion_auto', '13. Certificación AUTO'),
    ('registro', '14. Registro'),
    ('finalizado', '15. FINALIZADO'),
]

class TituloSupletorio(models.Model):
    """
    Modelo principal para el proceso de Título Supletorio en Guatemala.
    Contiene la información general del expediente.
    """
    
    # Información general del expediente
    numero_expediente = models.CharField(
        max_length=50, 
        unique=True, 
        verbose_name="Número de Expediente",
        help_text="Ej: TS-001-2025"
    )
    solicitante = models.CharField(
        max_length=200, 
        verbose_name="Nombre del Solicitante"
    )
    ubicacion_inmueble = models.TextField(
        verbose_name="Ubicación del Inmueble",
        help_text="Descripción detallada de la ubicación del inmueble"
    )
    departamento = models.CharField(
        max_length=100, 
        verbose_name="Departamento"
    )
    municipio = models.CharField(
        max_length=100, 
        verbose_name="Municipio"
    )
    
    # Metadatos
    creado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='titulos_supletorios_creados',
        verbose_name="Creado por"
    )
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación"
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización"
    )
    
    class Meta:
        verbose_name = "Título Supletorio"
        verbose_name_plural = "Títulos Supletorios"
        ordering = ['-fecha_creacion']
    
    def __str__(self):
        return f"{self.numero_expediente} - {self.solicitante}"
    
    def get_estado_actual(self):
        """Obtiene el estado actual basado en la última etapa completada"""
        ultima_etapa = self.etapas.filter(completada=True).order_by('-orden').first()
        if ultima_etapa:
            return ultima_etapa.tipo
        return 'memorial_inicial'
    
    def get_estado_display_numero(self):
        """Retorna el número de la etapa actual"""
        estado_actual = self.get_estado_actual()
        estados_numerados = dict(ESTADOS_PROCESO)
        return estados_numerados.get(estado_actual, estado_actual)
    
    def get_progreso_porcentaje(self):
        """Calcula el porcentaje de progreso basado en las etapas completadas"""
        total_etapas = len(ESTADOS_PROCESO)
        etapas_completadas = self.etapas.filter(completada=True).count()
        return round((etapas_completadas / total_etapas) * 100, 1)
    
    def get_siguiente_etapa(self):
        """Obtiene la siguiente etapa que debe completarse"""
        # Obtener todas las etapas ordenadas
        etapas_ordenadas = [estado[0] for estado in ESTADOS_PROCESO]
        
        # Encontrar etapas completadas
        etapas_completadas = set(
            self.etapas.filter(completada=True).values_list('tipo', flat=True)
        )
        
        # Encontrar la primera etapa no completada
        for etapa in etapas_ordenadas:
            if etapa not in etapas_completadas:
                return etapa
        
        # Si todas las etapas están completadas
        return 'finalizado'
    
    def inicializar_etapas(self):
        """Crea todas las etapas para este título supletorio"""
        for orden, (tipo, _) in enumerate(ESTADOS_PROCESO, 1):
            EtapaTituloSupletorio.objects.get_or_create(
                titulo=self,
                tipo=tipo,
                defaults={
                    'orden': orden,
                    'completada': False
                }
            )


class EtapaTituloSupletorio(models.Model):
    """
    Modelo para representar cada etapa individual del proceso de título supletorio.
    """
    titulo = models.ForeignKey(
        TituloSupletorio,
        on_delete=models.CASCADE,
        related_name='etapas',
        verbose_name="Título Supletorio"
    )
    tipo = models.CharField(
        max_length=30,
        choices=ESTADOS_PROCESO,
        verbose_name="Tipo de Etapa"
    )
    orden = models.PositiveIntegerField(
        verbose_name="Orden de la Etapa",
        help_text="Orden secuencial de la etapa (1-15)"
    )
    fecha = models.DateField(
        null=True, blank=True,
        verbose_name="Fecha de Completación"
    )
    observaciones = models.TextField(
        blank=True, null=True,
        verbose_name="Observaciones",
        help_text="Notas y observaciones sobre esta etapa"
    )
    documento = models.FileField(
        upload_to='titulos_supletorios/',
        null=True, blank=True,
        verbose_name="Documento",
        help_text="Archivo PDF o Word relacionado con esta etapa"
    )
    completada = models.BooleanField(
        default=False,
        verbose_name="Etapa Completada"
    )
    fecha_creacion = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Fecha de Creación"
    )
    fecha_actualizacion = models.DateTimeField(
        auto_now=True,
        verbose_name="Fecha de Actualización"
    )
    
    class Meta:
        verbose_name = "Etapa de Título Supletorio"
        verbose_name_plural = "Etapas de Títulos Supletorios"
        unique_together = ['titulo', 'tipo']
        ordering = ['orden']
    
    def __str__(self):
        estado_nombre = dict(ESTADOS_PROCESO).get(self.tipo, self.tipo)
        return f"{self.titulo.numero_expediente} - {estado_nombre}"
    
    def save(self, *args, **kwargs):
        # Marcar como completada si tiene fecha
        if self.fecha and not self.completada:
            self.completada = True
        elif not self.fecha and self.completada:
            self.completada = False
        super().save(*args, **kwargs)
    
    def get_nombre_etapa(self):
        """Obtiene el nombre completo de la etapa"""
        return dict(ESTADOS_PROCESO).get(self.tipo, self.tipo)
