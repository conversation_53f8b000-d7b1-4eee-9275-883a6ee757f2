{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {{ titulo_pagina }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- Información del expediente -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="alert-heading mb-2">
                                    <i class="fas fa-file-contract me-1"></i>
                                    {{ titulo.numero_expediente }} - {{ titulo.solicitante }}
                                </h6>
                                <p class="mb-1">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ titulo.ubicacion_inmueble|truncatechars:80 }}
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-map me-1"></i>
                                    {{ titulo.departamento }}, {{ titulo.municipio }}
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ titulo.get_progreso_porcentaje }}%"
                                         aria-valuenow="{{ titulo.get_progreso_porcentaje }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ titulo.get_progreso_porcentaje }}%
                                    </div>
                                </div>
                                <small class="text-muted">Progreso del proceso</small>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Campo de fecha -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.fecha.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        {{ form.fecha.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.fecha }}
                                    {% if form.fecha.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.fecha.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Campos adicionales para primera resolución -->
                            {% if etapa == 'primera_resolucion' %}
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.fecha_audiencia_experto.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ form.fecha_audiencia_experto.label }}
                                        </label>
                                        {{ form.fecha_audiencia_experto }}
                                        {% if form.fecha_audiencia_experto.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.fecha_audiencia_experto.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.fecha_audiencia_testigos.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ form.fecha_audiencia_testigos.label }}
                                        </label>
                                        {{ form.fecha_audiencia_testigos }}
                                        {% if form.fecha_audiencia_testigos.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.fecha_audiencia_testigos.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Campo de observaciones -->
                        <div class="mb-3">
                            <label for="{{ form.observaciones.id_for_label }}" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>
                                {{ form.observaciones.label }}
                            </label>
                            {{ form.observaciones }}
                            {% if form.observaciones.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.observaciones.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Campo de documento -->
                        <div class="mb-3">
                            <label for="{{ form.documento.id_for_label }}" class="form-label">
                                <i class="fas fa-file-upload me-1"></i>
                                {{ form.documento.label }}
                            </label>
                            {{ form.documento }}
                            {% if form.documento.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.documento.errors.0 }}
                                </div>
                            {% endif %}
                            {% if form.documento.help_text %}
                                <div class="form-text">
                                    {{ form.documento.help_text }}
                                </div>
                            {% endif %}
                            
                            <!-- Mostrar documento actual si existe -->
                            {% if etapa_obj.documento %}
                                <div class="mt-2">
                                    <div class="alert alert-info">
                                        <i class="fas fa-file me-1"></i>
                                        <strong>Documento actual:</strong>
                                        <a href="{{ etapa_obj.documento.url }}" target="_blank" class="alert-link">
                                            {{ etapa_obj.documento.name|slice:"30:" }}
                                        </a>
                                        <small class="text-muted d-block">
                                            Selecciona un nuevo archivo para reemplazarlo
                                        </small>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Información sobre la etapa -->
                        <div class="alert alert-light border">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-info-circle me-1"></i>
                                Información sobre esta etapa
                            </h6>
                            <p class="mb-0 small">
                                {% if etapa == 'memorial_inicial' %}
                                    El memorial inicial es el documento que da inicio al proceso de título supletorio. 
                                    Debe contener la solicitud formal y los fundamentos legales correspondientes.
                                {% elif etapa == 'primera_resolucion' %}
                                    En la primera resolución se establecen las fechas para las audiencias del experto medidor y de testigos.
                                    Es importante programar estas fechas con suficiente anticipación.
                                {% elif etapa == 'notificacion_colindantes' %}
                                    Se debe notificar a todos los propietarios colindantes sobre el proceso de título supletorio
                                    para que puedan ejercer su derecho de oposición si lo consideran necesario.
                                {% elif etapa == 'audiencia_testigos' %}
                                    En esta audiencia se recibe la declaración de los testigos que pueden dar fe de la posesión
                                    del solicitante sobre el inmueble objeto del proceso.
                                {% elif etapa == 'audiencia_experto' %}
                                    El experto medidor presenta su dictamen técnico sobre las medidas, colindancias y 
                                    características del inmueble objeto del título supletorio.
                                {% elif etapa == 'inspeccion_municipal' %}
                                    La municipalidad realiza una inspección del inmueble para verificar su situación actual
                                    y emitir su opinión técnica sobre el proceso.
                                {% elif etapa == 'informe_municipal' %}
                                    La municipalidad emite su informe oficial sobre el inmueble, incluyendo aspectos urbanísticos
                                    y de ordenamiento territorial.
                                {% elif etapa == 'edictos' %}
                                    Se publican los edictos correspondientes para dar publicidad al proceso y permitir
                                    que terceros interesados puedan hacer valer sus derechos.
                                {% elif etapa == 'publicaciones' %}
                                    Se realizan las publicaciones en medios oficiales según lo establecido por la ley
                                    para garantizar la publicidad del proceso.
                                {% elif etapa == 'memorial_experto' %}
                                    El experto medidor presenta su memorial final con todas las conclusiones técnicas
                                    del proceso de medición y verificación del inmueble.
                                {% elif etapa == 'memorial_publicaciones_pgn' %}
                                    Se presenta el memorial con las publicaciones realizadas y se remite el expediente
                                    a la Procuraduría General de la Nación (PGN) para su dictamen.
                                {% elif etapa == 'auto' %}
                                    El juez emite el auto final que resuelve sobre la procedencia del título supletorio
                                    basándose en toda la evidencia presentada durante el proceso.
                                {% elif etapa == 'certificacion_auto' %}
                                    Se certifica el auto emitido por el juez para que tenga plenos efectos legales
                                    y pueda ser utilizado para el registro correspondiente.
                                {% elif etapa == 'registro' %}
                                    Se procede al registro del título supletorio en el Registro General de la Propiedad
                                    para que surta todos los efectos legales correspondientes.
                                {% elif etapa == 'finalizado' %}
                                    El proceso ha sido completado exitosamente. El título supletorio ha sido registrado
                                    y el solicitante ya cuenta con el documento que acredita su derecho de propiedad.
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'detalle_titulo_supletorio' titulo.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Volver al Detalle
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Actualizar Etapa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar fecha actual por defecto si el campo está vacío
    const fechaInput = document.querySelector('input[type="date"][name="fecha"]');
    if (fechaInput && !fechaInput.value) {
        const today = new Date().toISOString().split('T')[0];
        fechaInput.value = today;
    }
    
    // Validación en tiempo real
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>

{% endblock %}
