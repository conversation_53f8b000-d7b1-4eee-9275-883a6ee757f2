{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        {{ titulo }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="POST" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.numero_expediente.id_for_label }}" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i>
                                        {{ form.numero_expediente.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.numero_expediente }}
                                    {% if form.numero_expediente.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.numero_expediente.errors.0 }}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        {{ form.numero_expediente.help_text }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.solicitante.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        {{ form.solicitante.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.solicitante }}
                                    {% if form.solicitante.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.solicitante.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.ubicacion_inmueble.id_for_label }}" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ form.ubicacion_inmueble.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.ubicacion_inmueble }}
                            {% if form.ubicacion_inmueble.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.ubicacion_inmueble.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ form.ubicacion_inmueble.help_text }}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.departamento.id_for_label }}" class="form-label">
                                        <i class="fas fa-map me-1"></i>
                                        {{ form.departamento.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.departamento }}
                                    {% if form.departamento.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.departamento.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.municipio.id_for_label }}" class="form-label">
                                        <i class="fas fa-city me-1"></i>
                                        {{ form.municipio.label }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.municipio }}
                                    {% if form.municipio.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.municipio.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            

                        </div>
                        
                        <!-- Información sobre el proceso -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-1"></i>
                                Información sobre el Proceso de Título Supletorio
                            </h6>
                            <p class="mb-2">
                                El proceso de título supletorio consta de <strong>15 etapas</strong> que deben completarse secuencialmente:
                            </p>
                            <ol class="mb-0 small">
                                <li>Memorial inicial</li>
                                <li>Primera resolución (fechas de audiencias)</li>
                                <li>Notificación colindantes</li>
                                <li>Audiencia Testigos</li>
                                <li>Audiencia Experto Medidor</li>
                                <li>Inspección Municipal</li>
                                <li>Informe Municipal</li>
                                <li>Edictos</li>
                                <li>Publicaciones</li>
                                <li>Memorial experto medidor</li>
                                <li>Memorial presentación de publicaciones y Remitir a PGN</li>
                                <li>AUTO</li>
                                <li>Certificación AUTO</li>
                                <li>Registro</li>
                                <li>FINALIZADO</li>
                            </ol>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'lista_titulos_supletorios' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Volver a la Lista
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Crear Título Supletorio
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validación en tiempo real
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>

{% endblock %}
