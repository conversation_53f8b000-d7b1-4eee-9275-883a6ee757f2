{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Dashboard - Títulos Supletorios
                    </h4>
                    <div>
                        <a href="{% url 'crear_titulo_supletorio' %}" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-1"></i>
                            Nuevo Título
                        </a>
                        <a href="{% url 'lista_titulos_supletorios' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i>
                            Ver Lista
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Estadísticas generales -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ total_titulos }}</h3>
                            <p class="mb-0">Total de Títulos</p>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-file-contract fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ titulos_finalizados }}</h3>
                            <p class="mb-0">Finalizados</p>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1">{{ titulos_en_proceso }}</h3>
                            <p class="mb-0">En Proceso</p>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Distribución por estados -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Distribución por Estados del Proceso
                    </h5>
                </div>
                <div class="card-body">
                    {% if estados_count %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Estado del Proceso</th>
                                        <th class="text-center">Cantidad</th>
                                        <th>Porcentaje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for estado_nombre, cantidad in estados_count.items %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-circle text-primary me-2"></i>
                                                {{ estado_nombre }}
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-primary">{{ cantidad }}</span>
                                            </td>
                                            <td>
                                                {% widthratio cantidad total_titulos 100 as porcentaje %}
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ porcentaje }}%"
                                                         aria-valuenow="{{ porcentaje }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        {{ porcentaje }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No hay datos para mostrar</h6>
                            <p class="text-muted">Crea tu primer título supletorio para ver las estadísticas.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Títulos recientes -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Títulos Recientes
                    </h5>
                </div>
                <div class="card-body">
                    {% if titulos_recientes %}
                        <div class="list-group list-group-flush">
                            {% for titulo in titulos_recientes %}
                                <div class="list-group-item px-0">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="{% url 'detalle_titulo_supletorio' titulo.pk %}" 
                                                   class="text-decoration-none">
                                                    {{ titulo.numero_expediente }}
                                                </a>
                                            </h6>
                                            <p class="mb-1 small text-muted">
                                                {{ titulo.solicitante }}
                                            </p>
                                            <small class="text-muted">
                                                {{ titulo.fecha_creacion|date:"d/m/Y" }}
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-primary small">
                                                {{ titulo.get_progreso_porcentaje }}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="{% url 'lista_titulos_supletorios' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>
                                Ver Todos
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No hay títulos recientes</h6>
                            <p class="text-muted small">Los títulos creados aparecerán aquí.</p>
                            <a href="{% url 'crear_titulo_supletorio' %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Crear Primer Título
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Información sobre el Proceso de Título Supletorio
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">¿Qué es un Título Supletorio?</h6>
                            <p class="small text-muted">
                                Es un procedimiento judicial mediante el cual los poseedores legítimos de tierras 
                                que carecen de inscripción en el Registro General de la Propiedad pueden obtener 
                                un documento legal para inscribir su derecho de posesión.
                            </p>
                            
                            <h6 class="text-primary">Requisitos Principales:</h6>
                            <ul class="small text-muted">
                                <li>Posesión legítima, continua, pacífica y pública</li>
                                <li>Período mínimo de 10 años de posesión</li>
                                <li>Buena fe en la posesión</li>
                                <li>Ausencia de inscripción registral previa</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary">Etapas del Proceso (15 total):</h6>
                            <div class="small text-muted">
                                <div class="row">
                                    <div class="col-6">
                                        <ol>
                                            <li>Memorial inicial</li>
                                            <li>Primera resolución</li>
                                            <li>Notificación colindantes</li>
                                            <li>Audiencia Testigos</li>
                                            <li>Audiencia Experto Medidor</li>
                                            <li>Inspección Municipal</li>
                                            <li>Informe Municipal</li>
                                            <li>Edictos</li>
                                        </ol>
                                    </div>
                                    <div class="col-6">
                                        <ol start="9">
                                            <li>Publicaciones</li>
                                            <li>Memorial experto medidor</li>
                                            <li>Memorial publicaciones y PGN</li>
                                            <li>AUTO</li>
                                            <li>Certificación AUTO</li>
                                            <li>Registro</li>
                                            <li>FINALIZADO</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.opacity-75 {
    opacity: 0.75;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>

{% endblock %}
