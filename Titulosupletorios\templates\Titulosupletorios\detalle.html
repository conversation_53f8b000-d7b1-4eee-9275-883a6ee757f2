{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header del expediente -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="card-title mb-1">
                            <i class="fas fa-file-contract me-2"></i>
                            {{ titulo.numero_expediente }}
                        </h4>
                        <p class="text-muted mb-0">{{ titulo.solicitante }}</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group" role="group">
                            <a href="{% url 'editar_titulo_supletorio' titulo.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>
                                Editar
                            </a>
                            <a href="{% url 'avanzar_etapa' titulo.pk %}" class="btn btn-success">
                                <i class="fas fa-step-forward me-1"></i>
                                Siguiente Etapa
                            </a>
                            <a href="{% url 'lista_titulos_supletorios' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Volver
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6><i class="fas fa-map-marker-alt me-1"></i> Ubicación del Inmueble:</h6>
                            <p class="text-muted">{{ titulo.ubicacion_inmueble }}</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-map me-1"></i> Departamento:</h6>
                                    <p class="text-muted">{{ titulo.departamento }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-city me-1"></i> Municipio:</h6>
                                    <p class="text-muted">{{ titulo.municipio }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6>Progreso del Proceso</h6>
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: {{ progreso_porcentaje }}%"
                                         aria-valuenow="{{ progreso_porcentaje }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ progreso_porcentaje }}%
                                    </div>
                                </div>
                                <p class="text-muted small">
                                    Estado actual: <strong>{{ titulo.get_estado_display_numero }}</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Timeline del proceso -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-timeline me-2"></i>
                        Proceso de Título Supletorio
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="timeline">
                        {% for etapa in etapas %}
                            <div class="timeline-item {% if etapa.completada %}completed{% elif etapa.tipo == titulo.get_siguiente_etapa %}active{% endif %}">
                                <div class="timeline-marker">
                                    {% if etapa.tipo == 'memorial_inicial' %}
                                        <i class="fas fa-file-alt"></i>
                                    {% elif etapa.tipo == 'primera_resolucion' %}
                                        <i class="fas fa-gavel"></i>
                                    {% elif etapa.tipo == 'notificacion_colindantes' %}
                                        <i class="fas fa-bell"></i>
                                    {% elif etapa.tipo == 'audiencia_testigos' %}
                                        <i class="fas fa-users"></i>
                                    {% elif etapa.tipo == 'audiencia_experto' %}
                                        <i class="fas fa-user-tie"></i>
                                    {% elif etapa.tipo == 'inspeccion_municipal' %}
                                        <i class="fas fa-search"></i>
                                    {% elif etapa.tipo == 'informe_municipal' %}
                                        <i class="fas fa-file-alt"></i>
                                    {% elif etapa.tipo == 'edictos' %}
                                        <i class="fas fa-newspaper"></i>
                                    {% elif etapa.tipo == 'publicaciones' %}
                                        <i class="fas fa-bullhorn"></i>
                                    {% elif etapa.tipo == 'memorial_experto' %}
                                        <i class="fas fa-clipboard-list"></i>
                                    {% elif etapa.tipo == 'memorial_publicaciones_pgn' %}
                                        <i class="fas fa-share"></i>
                                    {% elif etapa.tipo == 'auto' %}
                                        <i class="fas fa-stamp"></i>
                                    {% elif etapa.tipo == 'certificacion_auto' %}
                                        <i class="fas fa-certificate"></i>
                                    {% elif etapa.tipo == 'registro' %}
                                        <i class="fas fa-book"></i>
                                    {% elif etapa.tipo == 'finalizado' %}
                                        <i class="fas fa-check"></i>
                                    {% endif %}
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="timeline-title">{{ etapa.get_nombre_etapa }}</h6>
                                            {% if etapa.fecha %}
                                                <p class="text-success mb-1">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    Completado: {{ etapa.fecha|date:"d/m/Y" }}
                                                </p>
                                            {% endif %}
                                            {% if etapa.observaciones %}
                                                <p class="text-muted small mb-2">{{ etapa.observaciones|truncatewords:20 }}</p>
                                            {% endif %}
                                            {% if etapa.documento %}
                                                <p class="mb-1">
                                                    <i class="fas fa-file-pdf text-danger me-1"></i>
                                                    <a href="{{ etapa.documento.url }}" target="_blank" class="text-decoration-none">
                                                        Ver documento
                                                    </a>
                                                </p>
                                            {% endif %}
                                        </div>
                                        <a href="{% url 'actualizar_etapa' titulo.pk etapa.tipo %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-item.active .timeline-marker {
    background: #007bff;
    animation: pulse 2s infinite;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}

.timeline-item.active .timeline-content {
    border-left-color: #007bff;
    background: #e3f2fd;
}

.timeline-item.completed .timeline-content {
    border-left-color: #28a745;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}
</style>

{% endblock %}
