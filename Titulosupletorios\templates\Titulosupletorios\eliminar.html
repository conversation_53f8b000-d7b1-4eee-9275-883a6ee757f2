{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmar Eliminación
                    </h4>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-1"></i>
                            ¡Atención!
                        </h5>
                        <p class="mb-0">
                            Esta acción no se puede deshacer. Se eliminará permanentemente el título supletorio 
                            y toda la información asociada.
                        </p>
                    </div>
                    
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-file-contract me-1"></i>
                                Información del Expediente a Eliminar
                            </h6>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Expediente:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.numero_expediente }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Solicitante:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.solicitante }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Ubicación:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.ubicacion_inmueble|truncatechars:100 }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Departamento:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.departamento }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Municipio:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.municipio }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Estado Actual:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-primary">
                                        {{ titulo.get_estado_display_numero }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Progreso:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ titulo.get_progreso_porcentaje }}%"
                                             aria-valuenow="{{ titulo.get_progreso_porcentaje }}" 
                                             aria-valuemin="0" aria-valuemax="100">
                                            {{ titulo.get_progreso_porcentaje }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Fecha Creación:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.fecha_creacion|date:"d/m/Y H:i" }}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <strong>Creado por:</strong>
                                </div>
                                <div class="col-sm-8">
                                    {{ titulo.creado_por.first_name }} {{ titulo.creado_por.last_name }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <p class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            ¿Está seguro de que desea eliminar este título supletorio?
                        </p>
                    </div>
                    
                    <form method="POST" class="d-flex justify-content-between">
                        {% csrf_token %}
                        
                        <a href="{% url 'detalle_titulo_supletorio' titulo.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Cancelar
                        </a>
                        
                        <button type="submit" class="btn btn-danger" onclick="return confirmarEliminacion()">
                            <i class="fas fa-trash me-1"></i>
                            Sí, Eliminar Definitivamente
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmarEliminacion() {
    return confirm('¿Está completamente seguro de que desea eliminar este título supletorio?\n\nEsta acción NO se puede deshacer y se perderá toda la información del expediente {{ titulo.numero_expediente }}.');
}

// Prevenir envío accidental del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Agregar confirmación adicional
    form.addEventListener('submit', function(e) {
        if (!confirmarEliminacion()) {
            e.preventDefault();
            return false;
        }
        
        // Deshabilitar el botón para evitar doble envío
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Eliminando...';
    });
});
</script>

{% endblock %}
