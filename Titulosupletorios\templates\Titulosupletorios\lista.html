{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Títulos Supletorios
                    </h4>
                    <a href="{% url 'crear_titulo_supletorio' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Nuevo Título Supletorio
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filtros y búsqueda -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <form method="GET" class="d-flex">
                                <input type="text" name="q" class="form-control me-2" 
                                       placeholder="Buscar por expediente, solicitante, ubicación..." 
                                       value="{{ query }}">
                                <select name="estado" class="form-select me-2" style="max-width: 250px;">
                                    <option value="">Todos los estados</option>
                                    {% for estado_key, estado_nombre in estados_proceso %}
                                        <option value="{{ estado_key }}" 
                                                {% if estado_filtro == estado_key %}selected{% endif %}>
                                            {{ estado_nombre }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                {% if query or estado_filtro %}
                                    <a href="{% url 'lista_titulos_supletorios' %}" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-times"></i>
                                    </a>
                                {% endif %}
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'dashboard_titulos_supletorios' %}" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-1"></i>
                                Dashboard
                            </a>
                        </div>
                    </div>

                    <!-- Tabla de títulos supletorios -->
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Expediente</th>
                                        <th>Solicitante</th>
                                        <th>Ubicación</th>
                                        <th>Departamento/Municipio</th>
                                        <th>Estado Actual</th>
                                        <th>Progreso</th>
                                        <th>Fecha Creación</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for titulo in page_obj %}
                                        <tr>
                                            <td>
                                                <strong>{{ titulo.numero_expediente }}</strong>
                                            </td>
                                            <td>{{ titulo.solicitante }}</td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ titulo.ubicacion_inmueble|truncatechars:50 }}
                                                </small>
                                            </td>
                                            <td>
                                                {{ titulo.departamento }}<br>
                                                <small class="text-muted">{{ titulo.municipio }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    {{ titulo.get_estado_display_numero }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ titulo.get_progreso_porcentaje }}%"
                                                         aria-valuenow="{{ titulo.get_progreso_porcentaje }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        {{ titulo.get_progreso_porcentaje }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <small>{{ titulo.fecha_creacion|date:"d/m/Y" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{% url 'detalle_titulo_supletorio' titulo.pk %}" 
                                                       class="btn btn-sm btn-outline-info" title="Ver detalles">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'editar_titulo_supletorio' titulo.pk %}" 
                                                       class="btn btn-sm btn-outline-warning" title="Editar">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'eliminar_titulo_supletorio' titulo.pk %}" 
                                                       class="btn btn-sm btn-outline-danger" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginación -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Paginación de títulos supletorios">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    <li class="page-item active">
                                        <span class="page-link">
                                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if estado_filtro %}&estado={{ estado_filtro }}{% endif %}">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No se encontraron títulos supletorios</h5>
                            <p class="text-muted">
                                {% if query or estado_filtro %}
                                    No hay resultados para los filtros aplicados.
                                {% else %}
                                    Comienza creando tu primer título supletorio.
                                {% endif %}
                            </p>
                            <a href="{% url 'crear_titulo_supletorio' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Crear Título Supletorio
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
