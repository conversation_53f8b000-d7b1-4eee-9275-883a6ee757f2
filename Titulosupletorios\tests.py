from django.test import TestCase
from django.contrib.auth import get_user_model
from .models import TituloSupletorio

User = get_user_model()

class TituloSupletorioTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            rol='admin'
        )
        
    def test_crear_titulo_supletorio(self):
        titulo = TituloSupletorio.objects.create(
            numero_expediente='TS-001-2025',
            solicitante='<PERSON>',
            ubicacion_inmueble='Finca El Ejemplo, Aldea San José',
            departamento='Guatemala',
            municipio='Guatemala',
            creado_por=self.user
        )
        
        self.assertEqual(titulo.numero_expediente, 'TS-001-2025')
        self.assertEqual(titulo.solicitante, '<PERSON>')
        self.assertEqual(titulo.estado_actual, 'memorial_inicial')
        
    def test_str_representation(self):
        titulo = TituloSupletorio.objects.create(
            numero_expediente='TS-002-2025',
            solicitante='<PERSON>',
            ubicacion_inmueble='Finca La Esperanza',
            departamento='Quetzaltenango',
            municipio='Quetzaltenango',
            creado_por=self.user
        )
        
        expected_str = 'TS-002-2025 - María García'
        self.assertEqual(str(titulo), expected_str)
