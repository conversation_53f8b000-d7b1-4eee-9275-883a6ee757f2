from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from . import views

urlpatterns = [
    # Dashboard
    path('dashboard/', views.dashboard_titulos_supletorios, name='dashboard_titulos_supletorios'),

    # CRUD básico
    path('', views.lista_titulos_supletorios, name='lista_titulos_supletorios'),
    path('crear/', views.crear_titulo_supletorio, name='crear_titulo_supletorio'),
    path('<int:pk>/', views.detalle_titulo_supletorio, name='detalle_titulo_supletorio'),
    path('<int:pk>/editar/', views.editar_titulo_supletorio, name='editar_titulo_supletorio'),
    path('<int:pk>/eliminar/', views.eliminar_titulo_supletorio, name='eliminar_titulo_supletorio'),

    # Actualización de etapas específicas
    path('<int:pk>/etapa/<str:etapa>/', views.actualizar_etapa, name='actualizar_etapa'),

    # Avanzar a siguiente etapa
    path('<int:pk>/avanzar/', views.avanzar_etapa, name='avanzar_etapa'),
]

# Servir archivos de media en desarrollo
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
