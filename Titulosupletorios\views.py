from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import TituloSupletorio, EtapaTituloSupletorio, ESTADOS_PROCESO
from .forms import TituloSupletorioForm, EtapaForm, PrimeraResolucionForm

@login_required
def lista_titulos_supletorios(request):
    """Vista para listar todos los títulos supletorios"""
    
    # Búsqueda
    query = request.GET.get('q', '')
    estado_filtro = request.GET.get('estado', '')
    
    titulos = TituloSupletorio.objects.all()
    
    if query:
        titulos = titulos.filter(
            Q(numero_expediente__icontains=query) |
            Q(solicitante__icontains=query) |
            Q(ubicacion_inmueble__icontains=query) |
            Q(departamento__icontains=query) |
            Q(municipio__icontains=query)
        )
    
    if estado_filtro:
        # Filtrar por títulos que tengan una etapa específica completada
        titulos = titulos.filter(etapas__tipo=estado_filtro, etapas__completada=True)
    
    # Paginación
    paginator = Paginator(titulos, 10)  # 10 títulos por página
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'query': query,
        'estado_filtro': estado_filtro,
        'estados_proceso': ESTADOS_PROCESO,
    }
    
    return render(request, 'Titulosupletorios/lista.html', context)

@login_required
def crear_titulo_supletorio(request):
    """Vista para crear un nuevo título supletorio"""
    
    if request.method == 'POST':
        form = TituloSupletorioForm(request.POST)
        if form.is_valid():
            titulo = form.save(commit=False)
            titulo.creado_por = request.user
            titulo.save()
            
            # Inicializar todas las etapas
            titulo.inicializar_etapas()
            
            messages.success(request, f'Título supletorio {titulo.numero_expediente} creado exitosamente.')
            return redirect('detalle_titulo_supletorio', pk=titulo.pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = TituloSupletorioForm()
    
    context = {
        'form': form,
        'titulo': 'Crear Nuevo Título Supletorio'
    }
    
    return render(request, 'Titulosupletorios/crear.html', context)

@login_required
def detalle_titulo_supletorio(request, pk):
    """Vista para ver los detalles de un título supletorio"""
    
    titulo = get_object_or_404(TituloSupletorio, pk=pk)
    
    # Obtener todas las etapas ordenadas
    etapas = titulo.etapas.all().order_by('orden')
    
    context = {
        'titulo': titulo,
        'etapas': etapas,
        'progreso_porcentaje': titulo.get_progreso_porcentaje(),
    }
    
    return render(request, 'Titulosupletorios/detalle.html', context)

@login_required
def editar_titulo_supletorio(request, pk):
    """Vista para editar la información general de un título supletorio"""
    
    titulo = get_object_or_404(TituloSupletorio, pk=pk)
    
    if request.method == 'POST':
        form = TituloSupletorioForm(request.POST, instance=titulo)
        if form.is_valid():
            form.save()
            messages.success(request, f'Título supletorio {titulo.numero_expediente} actualizado exitosamente.')
            return redirect('detalle_titulo_supletorio', pk=titulo.pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = TituloSupletorioForm(instance=titulo)
    
    context = {
        'form': form,
        'titulo': titulo,
        'titulo_pagina': f'Editar {titulo.numero_expediente}'
    }
    
    return render(request, 'Titulosupletorios/editar.html', context)

@login_required
def actualizar_etapa(request, pk, etapa):
    """Vista para actualizar una etapa específica del proceso"""
    
    titulo = get_object_or_404(TituloSupletorio, pk=pk)
    
    # Obtener o crear la etapa
    etapa_obj, created = EtapaTituloSupletorio.objects.get_or_create(
        titulo=titulo,
        tipo=etapa,
        defaults={
            'orden': next((i for i, (key, _) in enumerate(ESTADOS_PROCESO, 1) if key == etapa), 1)
        }
    )
    
    # Nombres de etapas para mostrar
    nombres_etapas = dict(ESTADOS_PROCESO)
    
    if etapa not in [estado[0] for estado in ESTADOS_PROCESO]:
        messages.error(request, 'Etapa no válida.')
        return redirect('detalle_titulo_supletorio', pk=pk)
    
    # Usar formulario específico para primera resolución
    FormClass = PrimeraResolucionForm if etapa == 'primera_resolucion' else EtapaForm
    
    if request.method == 'POST':
        form = FormClass(request.POST, request.FILES, instance=etapa_obj)
        if form.is_valid():
            etapa_actualizada = form.save(commit=False)
            
            # Marcar como completada si tiene fecha
            if etapa_actualizada.fecha:
                etapa_actualizada.completada = True
            
            etapa_actualizada.save()
            
            # Para primera resolución, guardar fechas adicionales en observaciones
            if etapa == 'primera_resolucion' and isinstance(form, PrimeraResolucionForm):
                observaciones_adicionales = []
                if form.cleaned_data.get('fecha_audiencia_experto'):
                    observaciones_adicionales.append(f"Fecha audiencia experto: {form.cleaned_data['fecha_audiencia_experto']}")
                if form.cleaned_data.get('fecha_audiencia_testigos'):
                    observaciones_adicionales.append(f"Fecha audiencia testigos: {form.cleaned_data['fecha_audiencia_testigos']}")
                
                if observaciones_adicionales:
                    obs_existentes = etapa_actualizada.observaciones or ""
                    nuevas_obs = "\n".join(observaciones_adicionales)
                    etapa_actualizada.observaciones = f"{obs_existentes}\n{nuevas_obs}".strip()
                    etapa_actualizada.save()
            
            messages.success(request, f'Etapa "{nombres_etapas[etapa]}" actualizada exitosamente.')
            return redirect('detalle_titulo_supletorio', pk=pk)
        else:
            messages.error(request, 'Por favor corrige los errores en el formulario.')
    else:
        form = FormClass(instance=etapa_obj)
    
    context = {
        'form': form,
        'titulo': titulo,
        'etapa': etapa,
        'etapa_obj': etapa_obj,
        'nombre_etapa': nombres_etapas[etapa],
        'titulo_pagina': f'{nombres_etapas[etapa]} - {titulo.numero_expediente}'
    }
    
    return render(request, 'Titulosupletorios/actualizar_etapa.html', context)

@login_required
def eliminar_titulo_supletorio(request, pk):
    """Vista para eliminar un título supletorio"""
    
    titulo = get_object_or_404(TituloSupletorio, pk=pk)
    
    if request.method == 'POST':
        numero_expediente = titulo.numero_expediente
        titulo.delete()
        messages.success(request, f'Título supletorio {numero_expediente} eliminado exitosamente.')
        return redirect('lista_titulos_supletorios')
    
    context = {
        'titulo': titulo
    }
    
    return render(request, 'Titulosupletorios/eliminar.html', context)

@login_required
def dashboard_titulos_supletorios(request):
    """Vista del dashboard con estadísticas de títulos supletorios"""
    
    # Estadísticas generales
    total_titulos = TituloSupletorio.objects.count()
    titulos_finalizados = TituloSupletorio.objects.filter(
        etapas__tipo='finalizado', 
        etapas__completada=True
    ).count()
    titulos_en_proceso = total_titulos - titulos_finalizados
    
    # Títulos por estado (basado en última etapa completada)
    estados_count = {}
    for titulo in TituloSupletorio.objects.all():
        estado_actual = titulo.get_estado_actual()
        estado_nombre = dict(ESTADOS_PROCESO).get(estado_actual, estado_actual)
        estados_count[estado_nombre] = estados_count.get(estado_nombre, 0) + 1
    
    # Títulos recientes
    titulos_recientes = TituloSupletorio.objects.order_by('-fecha_creacion')[:5]
    
    context = {
        'total_titulos': total_titulos,
        'titulos_finalizados': titulos_finalizados,
        'titulos_en_proceso': titulos_en_proceso,
        'estados_count': estados_count,
        'titulos_recientes': titulos_recientes,
    }
    
    return render(request, 'Titulosupletorios/dashboard.html', context)

@login_required
def avanzar_etapa(request, pk):
    """Vista para avanzar a la siguiente etapa - redirige al formulario correspondiente"""
    
    titulo = get_object_or_404(TituloSupletorio, pk=pk)
    siguiente_etapa = titulo.get_siguiente_etapa()
    
    if siguiente_etapa == 'finalizado':
        # Verificar si ya está finalizado
        etapa_finalizada = titulo.etapas.filter(tipo='finalizado', completada=True).first()
        if etapa_finalizada:
            messages.info(request, 'El proceso ya está completamente finalizado.')
            return redirect('detalle_titulo_supletorio', pk=pk)
    
    # Redirigir al formulario de la siguiente etapa
    nombre_etapa = dict(ESTADOS_PROCESO).get(siguiente_etapa, siguiente_etapa)
    messages.info(request, f'Completando la siguiente etapa: {nombre_etapa}')
    return redirect('actualizar_etapa', pk=pk, etapa=siguiente_etapa)
