{% extends 'BaseInicio/base.html' %}
{% load static %}

{% block content %}
<br></br>
<div class="container">
    <!-- Sección para modificar datos del usuario -->
    <form action="" method="POST" enctype="multipart/form-data">{% csrf_token %}
        <div class="container overflow-hidden">
            <h3>Modificación de Usuario: <strong class="text-danger">{{ d }}</strong></h3>
            <hr><br>
            <div class="row gy-5">
                {% for field in form %}
                    {% if field.name != 'password' %}  <!-- Excluir campo de contraseña -->
                        <div class="col-4">
                            {{ field.label }}: {{ field }}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
        <br>
        <div class="container" align="center">
            <button class="btn btn-primary" type="submit">Modificar Usuario</button>
            <button class="btn btn-danger" type="button" onclick="window.location.href='{% url 'ListaUser' %}'">Cancelar</button>
        </div>
    </form>
    
    <hr>
    
    <!-- Sección para modificar la contraseña -->
    <form action="" method="POST">{% csrf_token %}
        <div class="container overflow-hidden">
            <h3>Cambiar Contraseña</h3>
            <hr><br>
            <div class="row gy-5">
                <div class="col-6">
                    <label for="nuevo">Nueva contraseña:</label>
                    <input type="password" name="nuevo" class="form-control" required>
                </div>
                <div class="col-6">
                    <label for="confirmar">Confirmar contraseña:</label>
                    <input type="password" name="confirmar" class="form-control" required>
                </div>
            </div>
        </div>
<br>
        <div class="container" align="center">
            <button class="btn btn-warning" type="submit">Actualizar Contraseña</button>
        </div>
    </form>
</div>
{% endblock %}
